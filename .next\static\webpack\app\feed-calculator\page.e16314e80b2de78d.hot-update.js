"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed-calculator/page",{

/***/ "(app-pages-browser)/./src/app/feed-calculator/page.tsx":
/*!******************************************!*\
  !*** ./src/app/feed-calculator/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FeedCalculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction FeedCalculator() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const printRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedFeed, setSelectedFeed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Animal requirements\n    const [animalWeight, setAnimalWeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(500);\n    const [milkProduction, setMilkProduction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [productionStage, setProductionStage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ذروة الإنتاج\");\n    // Feed data - moved before useState\n    const feedData = [\n        {\n            name: \"الذرة الصفراء\",\n            protein: 8.5,\n            energy: 3200,\n            fiber: 2.1,\n            price: 320\n        },\n        {\n            name: \"الشعير\",\n            protein: 11.5,\n            energy: 2800,\n            fiber: 5.2,\n            price: 270\n        },\n        {\n            name: \"كسبة فول الصويا\",\n            protein: 44.0,\n            energy: 2200,\n            fiber: 7.0,\n            price: 600\n        },\n        {\n            name: \"البرسيم الحجازي\",\n            protein: 18.0,\n            energy: 2400,\n            fiber: 25.0,\n            price: 230\n        },\n        {\n            name: \"التبن\",\n            protein: 4.0,\n            energy: 1800,\n            fiber: 40.0,\n            price: 110\n        },\n        {\n            name: \"النخالة\",\n            protein: 15.5,\n            energy: 2000,\n            fiber: 12.0,\n            price: 200\n        },\n        {\n            name: \"الدريس\",\n            protein: 12.0,\n            energy: 2100,\n            fiber: 28.0,\n            price: 140\n        },\n        {\n            name: \"السيلاج\",\n            protein: 8.0,\n            energy: 2300,\n            fiber: 22.0,\n            price: 130\n        }\n    ];\n    const [showPriceEditor, setShowPriceEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editableFeedData, setEditableFeedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(feedData);\n    // Ration composition\n    const [ration, setRation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Array(8).fill(null).map(()=>({\n            feedName: \"\",\n            quantity: 0,\n            protein: 0,\n            energy: 0,\n            cost: 0\n        })));\n    // Calculate requirements based on animal data\n    const calculateRequirements = ()=>{\n        // التأكد من أن القيم موجبة\n        const weight = Math.max(animalWeight || 0, 0);\n        const milk = Math.max(milkProduction || 0, 0);\n        const baseProtein = weight * 0.004 + milk * 0.08 // كجم بروتين\n        ;\n        const baseEnergy = weight * 0.05 + milk * 1.2 // ميجا كالوري\n        ;\n        const baseDryMatter = weight * 0.03 + milk * 0.4 // كجم مادة جافة\n        ;\n        const stageMultiplier = {\n            \"بداية الإدرار\": 1.2,\n            \"ذروة الإنتاج\": 1.3,\n            \"منتصف الإدرار\": 1.1,\n            \"نهاية الإدرار\": 0.9\n        }[productionStage] || 1.1;\n        return {\n            protein: Math.max(baseProtein * stageMultiplier, 0),\n            energy: Math.max(baseEnergy * stageMultiplier, 0),\n            dryMatter: Math.max(baseDryMatter * stageMultiplier, 0)\n        };\n    };\n    const requirements = calculateRequirements();\n    // Load saved prices on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedPrices = localStorage.getItem(\"customFeedPrices\");\n        if (savedPrices) {\n            try {\n                setEditableFeedData(JSON.parse(savedPrices));\n            } catch (error) {\n                console.error(\"Error loading saved prices:\", error);\n                setEditableFeedData(feedData);\n            }\n        }\n    }, []);\n    // Update ration calculations\n    const updateRationRow = (index, field, value)=>{\n        const newRation = [\n            ...ration\n        ];\n        if (field === \"feedName\") {\n            const selectedFeedData = editableFeedData.find((f)=>f.name === value);\n            newRation[index] = {\n                ...newRation[index],\n                feedName: value,\n                protein: selectedFeedData ? newRation[index].quantity * selectedFeedData.protein / 100 : 0,\n                energy: selectedFeedData ? newRation[index].quantity * selectedFeedData.energy / 1000 : 0,\n                cost: selectedFeedData ? newRation[index].quantity * selectedFeedData.price / 1000 : 0\n            };\n        } else if (field === \"quantity\") {\n            const selectedFeedData = editableFeedData.find((f)=>f.name === newRation[index].feedName);\n            const qty = Number(value) || 0;\n            newRation[index] = {\n                ...newRation[index],\n                quantity: qty,\n                protein: selectedFeedData ? qty * selectedFeedData.protein / 100 : 0,\n                energy: selectedFeedData ? qty * selectedFeedData.energy / 1000 : 0,\n                cost: selectedFeedData ? qty * selectedFeedData.price / 1000 : 0\n            };\n        }\n        setRation(newRation);\n    };\n    // Add new row\n    const addNewRow = ()=>{\n        setRation([\n            ...ration,\n            {\n                feedName: \"\",\n                quantity: 0,\n                protein: 0,\n                energy: 0,\n                cost: 0\n            }\n        ]);\n    };\n    // Delete row\n    const deleteRow = (index)=>{\n        if (ration.length > 1) {\n            const newRation = ration.filter((_, i)=>i !== index);\n            setRation(newRation);\n        } else {\n            // Reset the row if it's the last one\n            const newRation = [\n                ...ration\n            ];\n            newRation[index] = {\n                feedName: \"\",\n                quantity: 0,\n                protein: 0,\n                energy: 0,\n                cost: 0\n            };\n            setRation(newRation);\n        }\n    };\n    // Clear all rows\n    const clearAllRows = ()=>{\n        setRation(Array(5).fill(null).map(()=>({\n                feedName: \"\",\n                quantity: 0,\n                protein: 0,\n                energy: 0,\n                cost: 0\n            })));\n    };\n    // Update feed price\n    const updateFeedPrice = (feedName, newPrice)=>{\n        const updatedFeedData = editableFeedData.map((feed)=>feed.name === feedName ? {\n                ...feed,\n                price: newPrice\n            } : feed);\n        setEditableFeedData(updatedFeedData);\n        // Update existing ration calculations\n        const updatedRation = ration.map((row)=>{\n            if (row.feedName === feedName) {\n                const updatedFeed = updatedFeedData.find((f)=>f.name === feedName);\n                return {\n                    ...row,\n                    cost: updatedFeed ? row.quantity * updatedFeed.price / 1000 : row.cost\n                };\n            }\n            return row;\n        });\n        setRation(updatedRation);\n    };\n    // Save prices to localStorage\n    const savePrices = ()=>{\n        try {\n            localStorage.setItem(\"customFeedPrices\", JSON.stringify(editableFeedData));\n            alert(\"تم حفظ الأسعار بنجاح!\");\n        } catch (error) {\n            console.error(\"Error saving prices:\", error);\n            alert(\"حدث خطأ أثناء حفظ الأسعار!\");\n        }\n    };\n    // Load prices from localStorage\n    const loadPrices = ()=>{\n        try {\n            const savedPrices = localStorage.getItem(\"customFeedPrices\");\n            if (savedPrices) {\n                setEditableFeedData(JSON.parse(savedPrices));\n                alert(\"تم تحميل الأسعار المحفوظة!\");\n            } else {\n                alert(\"لا توجد أسعار محفوظة!\");\n            }\n        } catch (error) {\n            console.error(\"Error loading prices:\", error);\n            alert(\"حدث خطأ أثناء تحميل الأسعار!\");\n        }\n    };\n    // Reset prices to default\n    const resetPrices = ()=>{\n        setEditableFeedData([\n            ...feedData\n        ]);\n        localStorage.removeItem(\"customFeedPrices\");\n        alert(\"تم إعادة تعيين الأسعار للقيم الافتراضية!\");\n    };\n    // Calculate totals\n    const totals = ration.reduce((acc, row)=>({\n            quantity: acc.quantity + (Number(row.quantity) || 0),\n            protein: acc.protein + (Number(row.protein) || 0),\n            energy: acc.energy + (Number(row.energy) || 0),\n            cost: acc.cost + (Number(row.cost) || 0)\n        }), {\n        quantity: 0,\n        protein: 0,\n        energy: 0,\n        cost: 0\n    });\n    // Print function\n    const handlePrint = ()=>{\n        const printContent = printRef.current;\n        if (printContent) {\n            const printWindow = window.open(\"\", \"_blank\");\n            if (printWindow) {\n                printWindow.document.write(\"\\n          <html>\\n            <head>\\n              <title>تقرير تكوين العليقة</title>\\n              <style>\\n                body { font-family: Arial, sans-serif; direction: rtl; }\\n                table { width: 100%; border-collapse: collapse; margin: 20px 0; }\\n                th, td { border: 1px solid #000; padding: 8px; text-align: center; }\\n                th { background-color: #f0f0f0; }\\n                .header { text-align: center; margin-bottom: 30px; }\\n                .summary { margin: 20px 0; }\\n              </style>\\n            </head>\\n            <body>\\n              \".concat(printContent.innerHTML, \"\\n            </body>\\n          </html>\\n        \"));\n                printWindow.document.close();\n                printWindow.print();\n            }\n        }\n    };\n    // Save ration\n    const saveRation = ()=>{\n        const rationData = {\n            animalWeight,\n            milkProduction,\n            productionStage,\n            ration: ration.filter((row)=>row.feedName && row.quantity > 0),\n            totals,\n            requirements,\n            date: new Date().toLocaleDateString(\"ar-SA\")\n        };\n        localStorage.setItem(\"savedRation\", JSON.stringify(rationData));\n        alert(\"تم حفظ العليقة بنجاح!\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-4 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/\"),\n                                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                children: \"← العودة للصفحة الرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-center text-blue-800\",\n                                children: \"دراسة الجدوى الاقتصادية - برنامج تكوين العلائق\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: printRef,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"header\",\n                            style: {\n                                display: \"none\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    children: \"تقرير تكوين العليقة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"التاريخ: \",\n                                        new Date().toLocaleDateString(\"ar-SA\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"وزن الحيوان: \",\n                                        animalWeight,\n                                        \" كجم | إنتاج الحليب: \",\n                                        milkProduction,\n                                        \" لتر\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-12 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-600 text-white p-3 rounded-t-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-center font-bold\",\n                                                    children: \"مواد العلف المتاحة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-sm\",\n                                                    children: \"ترتيب حسب التكلفة المنخفضة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-300 rounded-b-lg\",\n                                            children: editableFeedData.map((feed, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 border-b border-gray-200 cursor-pointer hover:bg-blue-50 \".concat(selectedFeed === feed.name ? \"bg-blue-100\" : \"\"),\n                                                    onClick: ()=>setSelectedFeed(feed.name),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: feed.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: [\n                                                                        feed.price,\n                                                                        \" دينار\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"بروتين: \",\n                                                                feed.protein,\n                                                                \"% | طاقة: \",\n                                                                feed.energy\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-600 text-white p-3 rounded-t-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-center font-bold\",\n                                                children: \"جدول حساب تكوين العليقة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-300 rounded-b-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"bg-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"المادة العلفية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"الكمية (كجم)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"البروتين\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"الطاقة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"التكلفة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"الإجراءات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: [\n                                                            ration.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                className: \"w-full text-xs\",\n                                                                                value: row.feedName,\n                                                                                onChange: (e)=>updateRationRow(index, \"feedName\", e.target.value),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"اختر المادة\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                        lineNumber: 355,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    editableFeedData.map((feed, feedIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: feed.name,\n                                                                                            children: feed.name\n                                                                                        }, feedIndex, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                            lineNumber: 357,\n                                                                                            columnNumber: 29\n                                                                                        }, this))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                className: \"w-full text-xs p-1\",\n                                                                                placeholder: \"0\",\n                                                                                value: row.quantity || \"\",\n                                                                                onChange: (e)=>updateRationRow(index, \"quantity\", e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2 text-center\",\n                                                                            children: row.protein > 0 ? row.protein.toFixed(2) : \"--\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2 text-center\",\n                                                                            children: row.energy > 0 ? row.energy.toFixed(1) : \"--\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2 text-center\",\n                                                                            children: row.cost > 0 ? row.cost.toFixed(2) : \"--\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2 text-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1 justify-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>deleteRow(index),\n                                                                                        className: \"px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600\",\n                                                                                        title: \"حذف الصف\",\n                                                                                        children: \"\\uD83D\\uDDD1️\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                        lineNumber: 381,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            // Duplicate row\n                                                                                            const newRation = [\n                                                                                                ...ration\n                                                                                            ];\n                                                                                            newRation.splice(index + 1, 0, {\n                                                                                                ...row\n                                                                                            });\n                                                                                            setRation(newRation);\n                                                                                        },\n                                                                                        className: \"px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600\",\n                                                                                        title: \"نسخ الصف\",\n                                                                                        children: \"\\uD83D\\uDCCB\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                        lineNumber: 388,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 380,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 21\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"bg-yellow-100 font-bold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2\",\n                                                                        children: \"المجموع\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: totals.quantity.toFixed(1)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: totals.protein.toFixed(2)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: totals.energy.toFixed(1)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: totals.cost.toFixed(2)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: \"--\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-700 p-3 rounded-b-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center gap-2 flex-wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: addNewRow,\n                                                        className: \"px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600\",\n                                                        title: \"إضافة صف جديد\",\n                                                        children: \"➕ إضافة صف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: clearAllRows,\n                                                        className: \"px-3 py-2 bg-red-500 text-white rounded text-sm hover:bg-red-600\",\n                                                        title: \"مسح جميع الصفوف\",\n                                                        children: \"\\uD83D\\uDDD1️ مسح الكل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            // Auto calculate - already happening in real time\n                                                            alert(\"تم حساب التكوين تلقائياً!\");\n                                                        },\n                                                        className: \"px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\",\n                                                        children: \"\\uD83D\\uDCCA حساب التكوين\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: saveRation,\n                                                        className: \"px-3 py-2 bg-purple-500 text-white rounded text-sm hover:bg-purple-600\",\n                                                        children: \"\\uD83D\\uDCBE حفظ العليقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handlePrint,\n                                                        className: \"px-3 py-2 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600\",\n                                                        children: \"\\uD83D\\uDDA8️ طباعة النتائج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            const savedRation = localStorage.getItem(\"savedRation\");\n                                                            if (savedRation) {\n                                                                const rationData = JSON.parse(savedRation);\n                                                                setRation(rationData.ration || rationData);\n                                                                if (rationData.animalWeight) setAnimalWeight(rationData.animalWeight);\n                                                                if (rationData.milkProduction) setMilkProduction(rationData.milkProduction);\n                                                                if (rationData.productionStage) setProductionStage(rationData.productionStage);\n                                                                alert(\"تم تحميل العليقة المحفوظة!\");\n                                                            } else {\n                                                                alert(\"لا توجد عليقة محفوظة!\");\n                                                            }\n                                                        },\n                                                        className: \"px-3 py-2 bg-orange-500 text-white rounded text-sm hover:bg-orange-600\",\n                                                        children: \"\\uD83D\\uDCC2 تحميل العليقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowPriceEditor(true),\n                                                        className: \"px-3 py-2 bg-indigo-500 text-white rounded text-sm hover:bg-indigo-600\",\n                                                        children: \"\\uD83D\\uDCB0 تعديل الأسعار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 text-white p-3 rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-center font-bold text-sm\",\n                                                        children: \"احتياجات الحيوان\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-300 rounded-b-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-bold mb-1\",\n                                                                        children: \"وزن الحيوان (كجم)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        className: \"w-full p-2 border rounded\",\n                                                                        value: animalWeight,\n                                                                        onChange: (e)=>setAnimalWeight(Number(e.target.value) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-bold mb-1\",\n                                                                        children: \"إنتاج الحليب (لتر)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        className: \"w-full p-2 border rounded\",\n                                                                        value: milkProduction,\n                                                                        onChange: (e)=>setMilkProduction(Number(e.target.value) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-bold mb-1\",\n                                                                        children: \"مرحلة الإنتاج\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 520,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        className: \"w-full p-2 border rounded text-sm\",\n                                                                        value: productionStage,\n                                                                        onChange: (e)=>setProductionStage(e.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"بداية الإدرار\",\n                                                                                children: \"بداية الإدرار\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"ذروة الإنتاج\",\n                                                                                children: \"ذروة الإنتاج\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"منتصف الإدرار\",\n                                                                                children: \"منتصف الإدرار\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"نهاية الإدرار\",\n                                                                                children: \"نهاية الإدرار\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-orange-600 text-white p-3 rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-center font-bold text-sm\",\n                                                        children: \"التحليل الغذائي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-300 rounded-b-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"البروتين المطلوب:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            requirements.protein.toFixed(2),\n                                                                            \" كجم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"الطاقة المطلوبة:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            requirements.energy.toFixed(1),\n                                                                            \" ميجا كالوري\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"المادة الجافة:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            requirements.dryMatter.toFixed(1),\n                                                                            \" كجم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-green-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"البروتين المحسوب:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            totals.protein.toFixed(2),\n                                                                            \" كجم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-green-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"الطاقة المحسوبة:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            totals.energy.toFixed(1),\n                                                                            \" ميجا كالوري\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"نسبة تحقق البروتين:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold \".concat(totals.protein >= requirements.protein * 0.95 ? \"text-green-600\" : \"text-red-600\"),\n                                                                        children: [\n                                                                            requirements.protein > 0 ? Math.max(totals.protein / requirements.protein * 100, 0).toFixed(1) : \"0.0\",\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"نسبة تحقق الطاقة:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold \".concat(totals.energy >= requirements.energy * 0.95 ? \"text-green-600\" : \"text-red-600\"),\n                                                                        children: [\n                                                                            requirements.energy > 0 ? Math.max(totals.energy / requirements.energy * 100, 0).toFixed(1) : \"0.0\",\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-purple-600 text-white p-3 rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-center font-bold text-sm\",\n                                                        children: \"تحليل التكلفة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-300 rounded-b-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"تكلفة العليقة اليومية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            totals.cost.toFixed(2),\n                                                                            \" دينار\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"تكلفة شهرية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 597,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            (totals.cost * 30).toFixed(2),\n                                                                            \" دينار\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"تكلفة سنوية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            (totals.cost * 365).toFixed(2),\n                                                                            \" دينار\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-blue-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"تكلفة لتر الحليب:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            milkProduction > 0 ? Math.max(totals.cost / milkProduction, 0).toFixed(2) : \"0.00\",\n                                                                            \" دينار\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-purple-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"الربحية (إذا كان سعر اللتر 0.6 دينار):\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            milkProduction > 0 ? Math.max((0.6 - totals.cost / milkProduction) * milkProduction, 0).toFixed(2) : \"0.00\",\n                                                                            \" دينار/يوم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n                showPriceEditor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-blue-700\",\n                                        children: \"تعديل أسعار المواد العلفية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPriceEditor(false),\n                                        className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: editableFeedData.map((feed, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-gray-800\",\n                                                        children: feed.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"بروتين: \",\n                                                            feed.protein,\n                                                            \"% | طاقة: \",\n                                                            feed.energy,\n                                                            \" كالوري | ألياف: \",\n                                                            feed.fiber,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: feed.price,\n                                                        onChange: (e)=>updateFeedPrice(feed.name, Number(e.target.value) || 0),\n                                                        className: \"w-24 p-2 border rounded text-center\",\n                                                        min: \"0\",\n                                                        step: \"10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"دينار/طن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: savePrices,\n                                        className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n                                        children: \"\\uD83D\\uDCBE حفظ الأسعار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: loadPrices,\n                                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                        children: \"\\uD83D\\uDCC2 تحميل الأسعار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetPrices,\n                                        className: \"px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600\",\n                                        children: \"\\uD83D\\uDD04 إعادة تعيين\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPriceEditor(false),\n                                        className: \"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600\",\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedCalculator, \"e12M4rJHfL6/TFh4JsDFOiJXqWk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = FeedCalculator;\nvar _c;\n$RefreshReg$(_c, \"FeedCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed-calculator/page.tsx\n"));

/***/ })

});