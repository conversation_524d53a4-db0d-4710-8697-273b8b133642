'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

interface NutritionalRequirements {
  dryMatter: number
  crudeProtein: number
  digestibleEnergy: number
  calcium: number
  phosphorus: number
}

interface FeedComposition {
  concentrate: number
  roughage: number
  protein: number
  energy: number
  calcium: number
  phosphorus: number
}

export default function FeedComposition() {
  const router = useRouter()
  const [weight, setWeight] = useState<number>(0)
  const [milkProduction, setMilkProduction] = useState<number>(0)
  const [productionStage, setProductionStage] = useState<string>('')
  const [requirements, setRequirements] = useState<NutritionalRequirements | null>(null)
  const [feedComposition, setFeedComposition] = useState<FeedComposition | null>(null)
  const [showResults, setShowResults] = useState(false)

  const calculateRequirements = () => {
    if (!weight || !milkProduction || !productionStage) {
      alert('يرجى إدخال جميع البيانات المطلوبة')
      return
    }

    // حسابات الاحتياجات الغذائية بناءً على الوزن وإنتاج الحليب
    const baseMaintenanceEnergy = weight * 0.077 // ميجا كالوري للصيانة
    const milkEnergy = milkProduction * 0.75 // ميجا كالوري لإنتاج الحليب
    const totalEnergy = baseMaintenanceEnergy + milkEnergy

    // تعديل حسب مرحلة الإنتاج
    let stageMultiplier = 1.0
    switch (productionStage) {
      case 'بداية الإدرار':
        stageMultiplier = 1.2
        break
      case 'ذروة الإنتاج':
        stageMultiplier = 1.3
        break
      case 'منتصف الإدرار':
        stageMultiplier = 1.1
        break
      case 'نهاية الإدرار':
        stageMultiplier = 0.9
        break
      case 'فترة الجفاف':
        stageMultiplier = 0.8
        break
    }

    const adjustedEnergy = totalEnergy * stageMultiplier
    const dryMatter = weight * 0.03 + milkProduction * 0.15 // كجم مادة جافة
    const crudeProtein = dryMatter * 0.16 // 16% بروتين من المادة الجافة
    const calcium = milkProduction * 2.5 + weight * 0.04 // جرام كالسيوم
    const phosphorus = calcium * 0.8 // نسبة الفوسفور للكالسيوم

    const newRequirements: NutritionalRequirements = {
      dryMatter: Math.round(dryMatter * 10) / 10,
      crudeProtein: Math.round(crudeProtein * 100) / 100,
      digestibleEnergy: Math.round(adjustedEnergy * 10) / 10,
      calcium: Math.round(calcium),
      phosphorus: Math.round(phosphorus)
    }

    setRequirements(newRequirements)
    setShowResults(true)
  }

  const composeFeed = () => {
    if (!requirements) {
      alert('يرجى حساب الاحتياجات أولاً')
      return
    }

    // حساب تركيب العليقة
    const concentrateRatio = milkProduction > 20 ? 0.6 : milkProduction > 10 ? 0.5 : 0.4
    const roughageRatio = 1 - concentrateRatio

    const concentrate = requirements.dryMatter * concentrateRatio
    const roughage = requirements.dryMatter * roughageRatio

    const composition: FeedComposition = {
      concentrate: Math.round(concentrate * 10) / 10,
      roughage: Math.round(roughage * 10) / 10,
      protein: Math.round((requirements.crudeProtein / requirements.dryMatter) * 100 * 10) / 10,
      energy: Math.round((requirements.digestibleEnergy / requirements.dryMatter) * 10) / 10,
      calcium: requirements.calcium,
      phosphorus: requirements.phosphorus
    }

    setFeedComposition(composition)
  }

  const printResults = () => {
    if (!requirements) {
      alert('لا توجد نتائج للطباعة')
      return
    }

    const printContent = `
      تقرير الاحتياجات الغذائية
      ========================

      معلومات الحيوان:
      - الوزن: ${weight} كجم
      - إنتاج الحليب: ${milkProduction} لتر/يوم
      - مرحلة الإنتاج: ${productionStage}

      الاحتياجات الغذائية:
      - المادة الجافة: ${requirements.dryMatter} كجم/يوم
      - البروتين الخام: ${requirements.crudeProtein} كجم/يوم
      - الطاقة المهضومة: ${requirements.digestibleEnergy} ميجا كالوري/يوم
      - الكالسيوم: ${requirements.calcium} جرام/يوم
      - الفوسفور: ${requirements.phosphorus} جرام/يوم

      ${feedComposition ? `
      تركيب العليقة:
      - علف مركز: ${feedComposition.concentrate} كجم/يوم
      - علف خشن: ${feedComposition.roughage} كجم/يوم
      - نسبة البروتين: ${feedComposition.protein}%
      - الطاقة لكل كجم: ${feedComposition.energy} ميجا كالوري
      ` : ''}
    `

    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>تقرير الاحتياجات الغذائية</title>
            <style>
              body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
              h1 { color: #2563eb; text-align: center; }
              pre { white-space: pre-wrap; font-size: 14px; line-height: 1.6; }
            </style>
          </head>
          <body>
            <h1>برنامج تركيب الخلائط العلفية</h1>
            <pre>${printContent}</pre>
          </body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-300 via-blue-100 to-blue-300 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => router.back()}
            className="button-3d px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300"
          >
            ← العودة
          </button>
          <h1 className="text-3xl font-bold text-blue-900">تركيب خلائط علفية للأبقار</h1>
          <div></div>
        </div>

        {/* Main Content */}
        <div className="wood-texture rounded-lg p-8">
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h2 className="text-2xl font-bold text-center mb-6 text-green-700">
              نظام تركيب الخلائط العلفية
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Input Section */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-blue-700 mb-4">معلومات الحيوان</h3>

                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-bold mb-2">وزن الحيوان (كجم)</label>
                    <input
                      type="number"
                      value={weight || ''}
                      onChange={(e) => setWeight(Number(e.target.value))}
                      className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500"
                      placeholder="500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-bold mb-2">إنتاج الحليب اليومي (لتر)</label>
                    <input
                      type="number"
                      value={milkProduction || ''}
                      onChange={(e) => setMilkProduction(Number(e.target.value))}
                      className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500"
                      placeholder="40"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-bold mb-2">مرحلة الإنتاج</label>
                    <select
                      value={productionStage}
                      onChange={(e) => setProductionStage(e.target.value)}
                      className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500"
                    >
                      <option value="">اختر المرحلة</option>
                      <option value="بداية الإدرار">بداية الإدرار</option>
                      <option value="ذروة الإنتاج">ذروة الإنتاج</option>
                      <option value="منتصف الإدرار">منتصف الإدرار</option>
                      <option value="نهاية الإدرار">نهاية الإدرار</option>
                      <option value="فترة الجفاف">فترة الجفاف</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Results Section */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-green-700 mb-4">الاحتياجات الغذائية</h3>

                <div className="bg-green-50 p-4 rounded-lg border-2 border-green-200">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="font-bold">المادة الجافة:</span>
                      <span>{requirements ? `${requirements.dryMatter} كجم/يوم` : '-- كجم/يوم'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-bold">البروتين الخام:</span>
                      <span>{requirements ? `${requirements.crudeProtein} كجم/يوم` : '-- كجم/يوم'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-bold">الطاقة المهضومة:</span>
                      <span>{requirements ? `${requirements.digestibleEnergy} ميجا كالوري/يوم` : '-- ميجا كالوري/يوم'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-bold">الكالسيوم:</span>
                      <span>{requirements ? `${requirements.calcium} جرام/يوم` : '-- جرام/يوم'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-bold">الفوسفور:</span>
                      <span>{requirements ? `${requirements.phosphorus} جرام/يوم` : '-- جرام/يوم'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Feed Composition Results */}
            {feedComposition && (
              <div className="mt-6 p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
                <h3 className="text-xl font-bold text-blue-700 mb-4 text-center">تركيب العليقة المقترحة</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white p-3 rounded border">
                    <h4 className="font-bold text-green-600 mb-2">المكونات الأساسية:</h4>
                    <div className="space-y-1 text-sm">
                      <p>علف مركز: {feedComposition.concentrate} كجم/يوم</p>
                      <p>علف خشن: {feedComposition.roughage} كجم/يوم</p>
                      <p className="font-bold">المجموع: {(feedComposition.concentrate + feedComposition.roughage).toFixed(1)} كجم/يوم</p>
                    </div>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <h4 className="font-bold text-orange-600 mb-2">التركيب الغذائي:</h4>
                    <div className="space-y-1 text-sm">
                      <p>نسبة البروتين: {feedComposition.protein}%</p>
                      <p>الطاقة/كجم: {feedComposition.energy} ميجا كالوري</p>
                      <p>الكالسيوم: {feedComposition.calcium} جم/يوم</p>
                      <p>الفوسفور: {feedComposition.phosphorus} جم/يوم</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-center gap-4 mt-8">
              <button
                onClick={calculateRequirements}
                className="button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold transition-colors duration-300"
              >
                حساب الاحتياجات
              </button>
              <button
                onClick={composeFeed}
                disabled={!requirements}
                className={`button-3d px-8 py-3 rounded-lg font-bold transition-colors duration-300 ${
                  requirements
                    ? 'bg-blue-200 hover:bg-blue-300'
                    : 'bg-gray-200 cursor-not-allowed opacity-50'
                }`}
              >
                تركيب العليقة
              </button>
              <button
                onClick={printResults}
                disabled={!requirements}
                className={`button-3d px-8 py-3 rounded-lg font-bold transition-colors duration-300 ${
                  requirements
                    ? 'bg-yellow-200 hover:bg-yellow-300'
                    : 'bg-gray-200 cursor-not-allowed opacity-50'
                }`}
              >
                طباعة النتائج
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
