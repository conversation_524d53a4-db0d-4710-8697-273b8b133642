/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/animal-type/page";
exports.ids = ["app/animal-type/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fanimal-type%2Fpage&page=%2Fanimal-type%2Fpage&appPaths=%2Fanimal-type%2Fpage&pagePath=private-next-app-dir%2Fanimal-type%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fanimal-type%2Fpage&page=%2Fanimal-type%2Fpage&appPaths=%2Fanimal-type%2Fpage&pagePath=private-next-app-dir%2Fanimal-type%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'animal-type',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/animal-type/page.tsx */ \"(rsc)/./src/app/animal-type/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/animal-type/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/animal-type/page\",\n        pathname: \"/animal-type\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fanimal-type%2Fpage&page=%2Fanimal-type%2Fpage&appPaths=%2Fanimal-type%2Fpage&pagePath=private-next-app-dir%2Fanimal-type%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Canimal-type%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Canimal-type%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/animal-type/page.tsx */ \"(ssr)/./src/app/animal-type/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNzcmMlNUNhcHAlNUNhbmltYWwtdHlwZSU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZlZWQtZm9ybXVsYXRpb24tYXBwLz8xNTFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccXVyYWFuXFxcXERlc2t0b3BcXFxcYWxhZlxcXFxzcmNcXFxcYXBwXFxcXGFuaW1hbC10eXBlXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Canimal-type%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/animal-type/page.tsx":
/*!**************************************!*\
  !*** ./src/app/animal-type/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnimalType)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AnimalType() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [selectedAnimal, setSelectedAnimal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const animalTypes = [\n        {\n            name: \"أبقار الحليب\",\n            icon: \"\\uD83D\\uDC04\",\n            description: \"أبقار متخصصة في إنتاج الحليب\",\n            requirements: \"احتياجات عالية من البروتين والطاقة\"\n        },\n        {\n            name: \"أبقار اللحم\",\n            icon: \"\\uD83D\\uDC02\",\n            description: \"أبقار متخصصة في إنتاج اللحم\",\n            requirements: \"احتياجات عالية من الطاقة لزيادة الوزن\"\n        },\n        {\n            name: \"العجول الصغيرة\",\n            icon: \"\\uD83D\\uDC2E\",\n            description: \"عجول في مرحلة النمو\",\n            requirements: \"احتياجات عالية من البروتين للنمو\"\n        },\n        {\n            name: \"الأبقار الحامل\",\n            icon: \"\\uD83E\\uDD31\",\n            description: \"أبقار في فترة الحمل\",\n            requirements: \"احتياجات إضافية من الكالسيوم والفيتامينات\"\n        }\n    ];\n    const getAnimalDetails = (animalName)=>{\n        const details = {\n            \"أبقار الحليب\": {\n                weight: \"500-700 كجم\",\n                dailyMilk: \"20-40 لتر\",\n                dryMatter: \"18-22 كجم/يوم\",\n                protein: \"16-18%\",\n                energy: \"1.6-1.8 ميجا كالوري/كجم\",\n                calcium: \"0.7-0.9%\",\n                phosphorus: \"0.4-0.5%\"\n            },\n            \"أبقار اللحم\": {\n                weight: \"400-800 كجم\",\n                dailyGain: \"1-1.5 كجم/يوم\",\n                dryMatter: \"12-18 كجم/يوم\",\n                protein: \"12-14%\",\n                energy: \"1.2-1.4 ميجا كالوري/كجم\",\n                calcium: \"0.4-0.6%\",\n                phosphorus: \"0.3-0.4%\"\n            },\n            \"العجول الصغيرة\": {\n                weight: \"50-200 كجم\",\n                dailyGain: \"0.8-1.2 كجم/يوم\",\n                dryMatter: \"3-8 كجم/يوم\",\n                protein: \"18-20%\",\n                energy: \"1.8-2.0 ميجا كالوري/كجم\",\n                calcium: \"0.8-1.0%\",\n                phosphorus: \"0.5-0.6%\"\n            },\n            \"الأبقار الحامل\": {\n                weight: \"500-700 كجم\",\n                gestationPeriod: \"280 يوم\",\n                dryMatter: \"15-20 كجم/يوم\",\n                protein: \"14-16%\",\n                energy: \"1.4-1.6 ميجا كالوري/كجم\",\n                calcium: \"0.8-1.2%\",\n                phosphorus: \"0.5-0.7%\"\n            }\n        };\n        return details[animalName] || {};\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-300 via-blue-100 to-blue-300 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.back(),\n                            className: \"button-3d px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300\",\n                            children: \"← العودة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-blue-900\",\n                            children: \"أنواع الحيوانات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wood-texture rounded-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-center mb-6 text-green-700\",\n                                children: \"اختيار نوع الحيوان وتحديد الاحتياجات الغذائية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                                children: animalTypes.map((animal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedAnimal(animal.name),\n                                        className: `p-6 rounded-lg border-2 transition-all button-3d text-center ${selectedAnimal === animal.name ? \"bg-green-200 border-green-500 text-green-800\" : \"bg-gray-100 border-gray-300 hover:bg-gray-200\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-3\",\n                                                children: animal.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-lg mb-2\",\n                                                children: animal.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-2\",\n                                                children: animal.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600\",\n                                                children: animal.requirements\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            selectedAnimal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-6 rounded-lg border-2 border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-blue-800 mb-4 text-center\",\n                                        children: [\n                                            \"تفاصيل: \",\n                                            selectedAnimal\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-green-700 mb-3\",\n                                                        children: \"المعلومات الأساسية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: Object.entries(getAnimalDetails(selectedAnimal)).slice(0, 3).map(([key, value], index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            key === \"weight\" && \"الوزن:\",\n                                                                            key === \"dailyMilk\" && \"إنتاج الحليب اليومي:\",\n                                                                            key === \"dailyGain\" && \"الزيادة اليومية:\",\n                                                                            key === \"gestationPeriod\" && \"فترة الحمل:\",\n                                                                            key === \"dryMatter\" && \"المادة الجافة:\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600\",\n                                                                        children: value\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-green-700 mb-3\",\n                                                        children: \"الاحتياجات الغذائية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: Object.entries(getAnimalDetails(selectedAnimal)).slice(3).map(([key, value], index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            key === \"protein\" && \"البروتين الخام:\",\n                                                                            key === \"energy\" && \"الطاقة:\",\n                                                                            key === \"calcium\" && \"الكالسيوم:\",\n                                                                            key === \"phosphorus\" && \"الفوسفور:\",\n                                                                            key === \"dryMatter\" && \"المادة الجافة:\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                        lineNumber: 154,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-green-600\",\n                                                                        children: value\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 bg-yellow-50 p-4 rounded-lg border border-yellow-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-yellow-800 mb-2\",\n                                                children: \"توصيات التغذية:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-sm text-yellow-700 space-y-1\",\n                                                children: [\n                                                    selectedAnimal === \"أبقار الحليب\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• تقديم علف مركز عالي البروتين\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• توفير مياه نظيفة باستمرار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• إضافة الأملاح المعدنية والفيتامينات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• تقسيم الوجبات على 3-4 مرات يومياً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    selectedAnimal === \"أبقار اللحم\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• التركيز على الطاقة لزيادة الوزن\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• استخدام الحبوب كمصدر رئيسي للطاقة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• توفير علف خشن جيد الجودة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• مراقبة معدل النمو بانتظام\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    selectedAnimal === \"العجول الصغيرة\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• بدء التغذية بالحليب أو بديل الحليب\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• تقديم علف بادئ عالي البروتين\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• التدرج في إدخال العلف الخشن\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• مراقبة النمو والصحة العامة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    selectedAnimal === \"الأبقار الحامل\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• زيادة الكالسيوم في الثلث الأخير\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• تجنب السمنة المفرطة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• توفير فيتامين E والسيلينيوم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"• مراقبة حالة الجسم بانتظام\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/feed-composition\"),\n                                        className: \"button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold\",\n                                        children: \"تركيب العليقة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"button-3d px-8 py-3 rounded-lg bg-blue-200 hover:bg-blue-300 font-bold\",\n                                        children: \"حساب الاحتياجات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"button-3d px-8 py-3 rounded-lg bg-yellow-200 hover:bg-yellow-300 font-bold\",\n                                        children: \"طباعة التفاصيل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\animal-type\\\\page.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/animal-type/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"09bb1e9faddc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzIxZmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOWJiMWU5ZmFkZGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/animal-type/page.tsx":
/*!**************************************!*\
  !*** ./src/app/animal-type/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\alaf\src\app\animal-type\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"برنامج التغذية التطبيقية للأبقار\",\n    description: \"برنامج تكوين الأعلاف للأبقار\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO2tCQUNsQiw0RUFBQ0M7c0JBQU1KOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfYqNix2YbYp9mF2Kwg2KfZhNiq2LrYsNmK2Kkg2KfZhNiq2LfYqNmK2YLZitipINmE2YTYo9io2YLYp9ixJyxcbiAgZGVzY3JpcHRpb246ICfYqNix2YbYp9mF2Kwg2KrZg9mI2YrZhiDYp9mE2KPYudmE2KfZgSDZhNmE2KPYqNmC2KfYsScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImRpciIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fanimal-type%2Fpage&page=%2Fanimal-type%2Fpage&appPaths=%2Fanimal-type%2Fpage&pagePath=private-next-app-dir%2Fanimal-type%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();