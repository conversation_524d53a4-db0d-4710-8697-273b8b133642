'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

interface AttachmentFile {
  id: number
  name: string
  type: string
  size: string
  description: string
  downloadCount: number
  uploadDate: string
  category: string
}

export default function Attachments() {
  const router = useRouter()

  const [attachments, setAttachments] = useState<AttachmentFile[]>([
    {
      id: 1,
      name: 'جداول القيم الغذائية',
      type: 'PDF',
      size: '2.5 MB',
      description: 'جداول شاملة للقيم الغذائية لجميع مواد العلف',
      downloadCount: 245,
      uploadDate: '2024-01-15',
      category: 'جداول'
    },
    {
      id: 2,
      name: 'دليل تركيب العلائق',
      type: 'PDF',
      size: '1.8 MB',
      description: 'دليل مفصل لتركيب العلائق المتوازنة للحيوانات',
      downloadCount: 189,
      uploadDate: '2024-01-10',
      category: 'أدلة'
    },
    {
      id: 3,
      name: 'صور مواد العلف',
      type: 'ZIP',
      size: '15.2 MB',
      description: 'مجموعة صور عالية الجودة لمختلف مواد العلف',
      downloadCount: 156,
      uploadDate: '2024-01-08',
      category: 'صور'
    },
    {
      id: 4,
      name: 'نماذج حساب الاحتياجات',
      type: 'Excel',
      size: '850 KB',
      description: 'نماذج Excel لحساب الاحتياجات الغذائية للحيوانات',
      downloadCount: 312,
      uploadDate: '2024-01-05',
      category: 'نماذج'
    },
    {
      id: 5,
      name: 'فيديوهات تعليمية',
      type: 'MP4',
      size: '125 MB',
      description: 'مقاطع فيديو تعليمية لتركيب العلائق',
      downloadCount: 98,
      uploadDate: '2024-01-01',
      category: 'فيديو'
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('الكل')
  const [sortBy, setSortBy] = useState('name')
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [newFile, setNewFile] = useState({
    name: '',
    type: '',
    size: '',
    description: '',
    category: ''
  })

  const categories = ['الكل', 'جداول', 'أدلة', 'صور', 'نماذج', 'فيديو']

  const filteredAttachments = attachments
    .filter(file =>
      file.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (selectedCategory === 'الكل' || file.category === selectedCategory)
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'date':
          return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime()
        case 'downloads':
          return b.downloadCount - a.downloadCount
        case 'size':
          return parseFloat(a.size) - parseFloat(b.size)
        default:
          return 0
      }
    })

  const handleDownload = (file: AttachmentFile) => {
    // محاكاة تحميل الملف
    setAttachments(attachments.map(att =>
      att.id === file.id
        ? { ...att, downloadCount: att.downloadCount + 1 }
        : att
    ))

    // إنشاء رابط تحميل وهمي
    const link = document.createElement('a')
    link.href = '#'
    link.download = file.name
    link.click()

    alert(`تم بدء تحميل: ${file.name}`)
  }

  const handleUpload = () => {
    if (newFile.name && newFile.type && newFile.category) {
      const id = Math.max(...attachments.map(att => att.id)) + 1
      const newAttachment: AttachmentFile = {
        ...newFile,
        id,
        downloadCount: 0,
        uploadDate: new Date().toISOString().split('T')[0],
        size: newFile.size || '1.0 MB'
      }

      setAttachments([...attachments, newAttachment])
      setNewFile({ name: '', type: '', size: '', description: '', category: '' })
      setShowUploadForm(false)
      alert('تم رفع الملف بنجاح!')
    } else {
      alert('يرجى إدخال جميع البيانات المطلوبة')
    }
  }

  const handleDelete = (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا الملف؟')) {
      setAttachments(attachments.filter(att => att.id !== id))
    }
  }

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf': return '📄'
      case 'excel': return '📊'
      case 'zip': return '📦'
      case 'mp4': return '🎥'
      default: return '📁'
    }
  }

  const getTotalSize = () => {
    return attachments.reduce((total, file) => {
      const size = parseFloat(file.size)
      const unit = file.size.includes('MB') ? 1 : file.size.includes('KB') ? 0.001 : 1
      return total + (size * unit)
    }, 0).toFixed(1)
  }

  const getTotalDownloads = () => {
    return attachments.reduce((total, file) => total + file.downloadCount, 0)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-300 via-blue-100 to-blue-300 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => router.back()}
            className="button-3d px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300"
          >
            ← العودة
          </button>
          <h1 className="text-3xl font-bold text-blue-900">الملفات والمرفقات المساعدة</h1>
          <div></div>
        </div>

        {/* Main Content */}
        <div className="wood-texture rounded-lg p-8">
          <div className="bg-white rounded-lg p-6 shadow-lg">

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg border-2 border-blue-200 text-center">
                <h4 className="font-bold text-blue-700">إجمالي الملفات</h4>
                <p className="text-2xl font-bold text-blue-900">{attachments.length}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg border-2 border-green-200 text-center">
                <h4 className="font-bold text-green-700">إجمالي التحميلات</h4>
                <p className="text-2xl font-bold text-green-900">{getTotalDownloads()}</p>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg border-2 border-orange-200 text-center">
                <h4 className="font-bold text-orange-700">إجمالي الحجم</h4>
                <p className="text-2xl font-bold text-orange-900">{getTotalSize()} MB</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg border-2 border-purple-200 text-center">
                <h4 className="font-bold text-purple-700">الفئات</h4>
                <p className="text-2xl font-bold text-purple-900">{categories.length - 1}</p>
              </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="mb-6 space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="البحث في الملفات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500"
                  />
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500"
                >
                  <option value="name">ترتيب بالاسم</option>
                  <option value="date">ترتيب بالتاريخ</option>
                  <option value="downloads">ترتيب بالتحميلات</option>
                  <option value="size">ترتيب بالحجم</option>
                </select>
              </div>

              <button
                onClick={() => setShowUploadForm(true)}
                className="button-3d px-6 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold"
              >
                + إضافة ملف جديد
              </button>
            </div>

            {/* Upload Form */}
            {showUploadForm && (
              <div className="mb-6 p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
                <h3 className="text-lg font-bold text-blue-700 mb-4">رفع ملف جديد</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="اسم الملف"
                    value={newFile.name}
                    onChange={(e) => setNewFile({...newFile, name: e.target.value})}
                    className="p-2 border rounded focus:border-blue-500"
                  />
                  <select
                    value={newFile.type}
                    onChange={(e) => setNewFile({...newFile, type: e.target.value})}
                    className="p-2 border rounded focus:border-blue-500"
                  >
                    <option value="">نوع الملف</option>
                    <option value="PDF">PDF</option>
                    <option value="Excel">Excel</option>
                    <option value="ZIP">ZIP</option>
                    <option value="MP4">MP4</option>
                    <option value="DOC">DOC</option>
                  </select>
                  <input
                    type="text"
                    placeholder="حجم الملف (مثل: 2.5 MB)"
                    value={newFile.size}
                    onChange={(e) => setNewFile({...newFile, size: e.target.value})}
                    className="p-2 border rounded focus:border-blue-500"
                  />
                  <select
                    value={newFile.category}
                    onChange={(e) => setNewFile({...newFile, category: e.target.value})}
                    className="p-2 border rounded focus:border-blue-500"
                  >
                    <option value="">الفئة</option>
                    {categories.slice(1).map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                  <textarea
                    placeholder="وصف الملف"
                    value={newFile.description}
                    onChange={(e) => setNewFile({...newFile, description: e.target.value})}
                    className="p-2 border rounded focus:border-blue-500 md:col-span-2"
                    rows={3}
                  />
                </div>
                <div className="flex gap-2 mt-4">
                  <button
                    onClick={handleUpload}
                    className="button-3d px-4 py-2 rounded bg-green-200 hover:bg-green-300 font-bold"
                  >
                    رفع الملف
                  </button>
                  <button
                    onClick={() => setShowUploadForm(false)}
                    className="button-3d px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 font-bold"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            )}

            {/* Files Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {filteredAttachments.map((file) => (
                <div key={file.id} className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border-2 border-blue-200 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl">{getFileIcon(file.type)}</span>
                      <div>
                        <h3 className="font-bold text-blue-800 text-sm">{file.name}</h3>
                        <p className="text-xs text-gray-600">{file.type} - {file.size}</p>
                      </div>
                    </div>
                    <span className="bg-blue-200 text-blue-800 px-2 py-1 rounded text-xs font-bold">
                      {file.category}
                    </span>
                  </div>

                  <p className="text-sm text-gray-700 mb-3 line-clamp-2">{file.description}</p>

                  <div className="flex items-center justify-between text-xs text-gray-600 mb-3">
                    <span>📅 {file.uploadDate}</span>
                    <span>⬇️ {file.downloadCount} تحميل</span>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => handleDownload(file)}
                      className="flex-1 button-3d px-3 py-2 rounded bg-green-200 hover:bg-green-300 text-sm font-bold"
                    >
                      تحميل
                    </button>
                    <button
                      onClick={() => handleDelete(file.id)}
                      className="button-3d px-3 py-2 rounded bg-red-200 hover:bg-red-300 text-sm font-bold"
                    >
                      حذف
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* No Results Message */}
            {filteredAttachments.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 text-lg">لا توجد ملفات تطابق البحث</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-center gap-4 mt-8">
              <button
                onClick={() => setShowUploadForm(true)}
                className="button-3d px-8 py-3 rounded-lg bg-blue-200 hover:bg-blue-300 font-bold transition-colors duration-300"
              >
                إضافة ملف جديد
              </button>
              <button
                onClick={() => window.location.reload()}
                className="button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold transition-colors duration-300"
              >
                تحديث المرفقات
              </button>
              <button
                onClick={() => {
                  const totalFiles = attachments.length
                  const totalSize = getTotalSize()
                  const totalDownloads = getTotalDownloads()
                  alert(`إحصائيات المرفقات:\n\nإجمالي الملفات: ${totalFiles}\nإجمالي الحجم: ${totalSize} MB\nإجمالي التحميلات: ${totalDownloads}`)
                }}
                className="button-3d px-8 py-3 rounded-lg bg-yellow-200 hover:bg-yellow-300 font-bold transition-colors duration-300"
              >
                عرض الإحصائيات
              </button>
            </div>

            {/* Help Section */}
            <div className="mt-8 bg-yellow-50 p-4 rounded-lg border-2 border-yellow-300">
              <h3 className="font-bold text-yellow-800 mb-2">تعليمات الاستخدام:</h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• استخدم شريط البحث للعثور على ملفات محددة</li>
                <li>• اختر فئة معينة لتصفية الملفات</li>
                <li>• يمكنك ترتيب الملفات حسب الاسم أو التاريخ أو عدد التحميلات</li>
                <li>• اضغط "تحميل" لتنزيل الملف (سيزيد عداد التحميلات)</li>
                <li>• يمكنك إضافة ملفات جديدة باستخدام نموذج الرفع</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
