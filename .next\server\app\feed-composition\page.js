/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/feed-composition/page";
exports.ids = ["app/feed-composition/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffeed-composition%2Fpage&page=%2Ffeed-composition%2Fpage&appPaths=%2Ffeed-composition%2Fpage&pagePath=private-next-app-dir%2Ffeed-composition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffeed-composition%2Fpage&page=%2Ffeed-composition%2Fpage&appPaths=%2Ffeed-composition%2Fpage&pagePath=private-next-app-dir%2Ffeed-composition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'feed-composition',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/feed-composition/page.tsx */ \"(rsc)/./src/app/feed-composition/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/feed-composition/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/feed-composition/page\",\n        pathname: \"/feed-composition\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffeed-composition%2Fpage&page=%2Ffeed-composition%2Fpage&appPaths=%2Ffeed-composition%2Fpage&pagePath=private-next-app-dir%2Ffeed-composition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNhcHAtcm91dGVyLmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3F1cmFhbiU1Q0Rlc2t0b3AlNUNhbGFmJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3F1cmFhbiU1Q0Rlc2t0b3AlNUNhbGFmJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWtJO0FBQ2xJLDBPQUFzSTtBQUN0SSx3T0FBcUk7QUFDckksa1BBQTBJO0FBQzFJLHNRQUFvSjtBQUNwSiIsInNvdXJjZXMiOlsid2VicGFjazovL2ZlZWQtZm9ybXVsYXRpb24tYXBwLz9iMjU1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccXVyYWFuXFxcXERlc2t0b3BcXFxcYWxhZlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHF1cmFhblxcXFxEZXNrdG9wXFxcXGFsYWZcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccXVyYWFuXFxcXERlc2t0b3BcXFxcYWxhZlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHF1cmFhblxcXFxEZXNrdG9wXFxcXGFsYWZcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHF1cmFhblxcXFxEZXNrdG9wXFxcXGFsYWZcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxxdXJhYW5cXFxcRGVza3RvcFxcXFxhbGFmXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcc3RhdGljLWdlbmVyYXRpb24tc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJvdmlkZXIuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cfeed-composition%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cfeed-composition%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/feed-composition/page.tsx */ \"(ssr)/./src/app/feed-composition/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNzcmMlNUNhcHAlNUNmZWVkLWNvbXBvc2l0aW9uJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvP2U1MzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxxdXJhYW5cXFxcRGVza3RvcFxcXFxhbGFmXFxcXHNyY1xcXFxhcHBcXFxcZmVlZC1jb21wb3NpdGlvblxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cfeed-composition%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/feed-composition/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/feed-composition/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedComposition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction FeedComposition() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [weight, setWeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [milkProduction, setMilkProduction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [productionStage, setProductionStage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [requirements, setRequirements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedComposition, setFeedComposition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const calculateRequirements = ()=>{\n        if (!weight || !milkProduction || !productionStage) {\n            alert(\"يرجى إدخال جميع البيانات المطلوبة\");\n            return;\n        }\n        // حسابات الاحتياجات الغذائية بناءً على الوزن وإنتاج الحليب\n        const baseMaintenanceEnergy = weight * 0.077 // ميجا كالوري للصيانة\n        ;\n        const milkEnergy = milkProduction * 0.75 // ميجا كالوري لإنتاج الحليب\n        ;\n        const totalEnergy = baseMaintenanceEnergy + milkEnergy;\n        // تعديل حسب مرحلة الإنتاج\n        let stageMultiplier = 1.0;\n        switch(productionStage){\n            case \"بداية الإدرار\":\n                stageMultiplier = 1.2;\n                break;\n            case \"ذروة الإنتاج\":\n                stageMultiplier = 1.3;\n                break;\n            case \"منتصف الإدرار\":\n                stageMultiplier = 1.1;\n                break;\n            case \"نهاية الإدرار\":\n                stageMultiplier = 0.9;\n                break;\n            case \"فترة الجفاف\":\n                stageMultiplier = 0.8;\n                break;\n        }\n        const adjustedEnergy = totalEnergy * stageMultiplier;\n        const dryMatter = weight * 0.03 + milkProduction * 0.15 // كجم مادة جافة\n        ;\n        const crudeProtein = dryMatter * 0.16 // 16% بروتين من المادة الجافة\n        ;\n        const calcium = milkProduction * 2.5 + weight * 0.04 // جرام كالسيوم\n        ;\n        const phosphorus = calcium * 0.8 // نسبة الفوسفور للكالسيوم\n        ;\n        const newRequirements = {\n            dryMatter: Math.round(dryMatter * 10) / 10,\n            crudeProtein: Math.round(crudeProtein * 100) / 100,\n            digestibleEnergy: Math.round(adjustedEnergy * 10) / 10,\n            calcium: Math.round(calcium),\n            phosphorus: Math.round(phosphorus)\n        };\n        setRequirements(newRequirements);\n        setShowResults(true);\n    };\n    const composeFeed = ()=>{\n        if (!requirements) {\n            alert(\"يرجى حساب الاحتياجات أولاً\");\n            return;\n        }\n        // حساب تركيب العليقة\n        const concentrateRatio = milkProduction > 20 ? 0.6 : milkProduction > 10 ? 0.5 : 0.4;\n        const roughageRatio = 1 - concentrateRatio;\n        const concentrate = requirements.dryMatter * concentrateRatio;\n        const roughage = requirements.dryMatter * roughageRatio;\n        const composition = {\n            concentrate: Math.round(concentrate * 10) / 10,\n            roughage: Math.round(roughage * 10) / 10,\n            protein: Math.round(requirements.crudeProtein / requirements.dryMatter * 100 * 10) / 10,\n            energy: Math.round(requirements.digestibleEnergy / requirements.dryMatter * 10) / 10,\n            calcium: requirements.calcium,\n            phosphorus: requirements.phosphorus\n        };\n        setFeedComposition(composition);\n    };\n    const printResults = ()=>{\n        if (!requirements) {\n            alert(\"لا توجد نتائج للطباعة\");\n            return;\n        }\n        const printContent = `\n      تقرير الاحتياجات الغذائية\n      ========================\n\n      معلومات الحيوان:\n      - الوزن: ${weight} كجم\n      - إنتاج الحليب: ${milkProduction} لتر/يوم\n      - مرحلة الإنتاج: ${productionStage}\n\n      الاحتياجات الغذائية:\n      - المادة الجافة: ${requirements.dryMatter} كجم/يوم\n      - البروتين الخام: ${requirements.crudeProtein} كجم/يوم\n      - الطاقة المهضومة: ${requirements.digestibleEnergy} ميجا كالوري/يوم\n      - الكالسيوم: ${requirements.calcium} جرام/يوم\n      - الفوسفور: ${requirements.phosphorus} جرام/يوم\n\n      ${feedComposition ? `\n      تركيب العليقة:\n      - علف مركز: ${feedComposition.concentrate} كجم/يوم\n      - علف خشن: ${feedComposition.roughage} كجم/يوم\n      - نسبة البروتين: ${feedComposition.protein}%\n      - الطاقة لكل كجم: ${feedComposition.energy} ميجا كالوري\n      ` : \"\"}\n    `;\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            printWindow.document.write(`\n        <html>\n          <head>\n            <title>تقرير الاحتياجات الغذائية</title>\n            <style>\n              body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }\n              h1 { color: #2563eb; text-align: center; }\n              pre { white-space: pre-wrap; font-size: 14px; line-height: 1.6; }\n            </style>\n          </head>\n          <body>\n            <h1>برنامج تركيب الخلائط العلفية</h1>\n            <pre>${printContent}</pre>\n          </body>\n        </html>\n      `);\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-300 via-blue-100 to-blue-300 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.back(),\n                            className: \"button-3d px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300\",\n                            children: \"← العودة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-blue-900\",\n                            children: \"تركيب خلائط علفية للأبقار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wood-texture rounded-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-center mb-6 text-green-700\",\n                                children: \"نظام تركيب الخلائط العلفية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-blue-700 mb-4\",\n                                                children: \"معلومات الحيوان\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-bold mb-2\",\n                                                                children: \"وزن الحيوان (كجم)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: weight || \"\",\n                                                                onChange: (e)=>setWeight(Number(e.target.value)),\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\",\n                                                                placeholder: \"500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-bold mb-2\",\n                                                                children: \"إنتاج الحليب اليومي (لتر)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: milkProduction || \"\",\n                                                                onChange: (e)=>setMilkProduction(Number(e.target.value)),\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\",\n                                                                placeholder: \"40\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-bold mb-2\",\n                                                                children: \"مرحلة الإنتاج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: productionStage,\n                                                                onChange: (e)=>setProductionStage(e.target.value),\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر المرحلة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"بداية الإدرار\",\n                                                                        children: \"بداية الإدرار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ذروة الإنتاج\",\n                                                                        children: \"ذروة الإنتاج\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"منتصف الإدرار\",\n                                                                        children: \"منتصف الإدرار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"نهاية الإدرار\",\n                                                                        children: \"نهاية الإدرار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"فترة الجفاف\",\n                                                                        children: \"فترة الجفاف\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-green-700 mb-4\",\n                                                children: \"الاحتياجات الغذائية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg border-2 border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"المادة الجافة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? `${requirements.dryMatter} كجم/يوم` : \"-- كجم/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"البروتين الخام:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? `${requirements.crudeProtein} كجم/يوم` : \"-- كجم/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"الطاقة المهضومة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? `${requirements.digestibleEnergy} ميجا كالوري/يوم` : \"-- ميجا كالوري/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"الكالسيوم:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? `${requirements.calcium} جرام/يوم` : \"-- جرام/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"الفوسفور:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? `${requirements.phosphorus} جرام/يوم` : \"-- جرام/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            feedComposition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-blue-50 rounded-lg border-2 border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-blue-700 mb-4 text-center\",\n                                        children: \"تركيب العليقة المقترحة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white p-3 rounded border\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-green-600 mb-2\",\n                                                        children: \"المكونات الأساسية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"علف مركز: \",\n                                                                    feedComposition.concentrate,\n                                                                    \" كجم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"علف خشن: \",\n                                                                    feedComposition.roughage,\n                                                                    \" كجم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-bold\",\n                                                                children: [\n                                                                    \"المجموع: \",\n                                                                    (feedComposition.concentrate + feedComposition.roughage).toFixed(1),\n                                                                    \" كجم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white p-3 rounded border\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-orange-600 mb-2\",\n                                                        children: \"التركيب الغذائي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"نسبة البروتين: \",\n                                                                    feedComposition.protein,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"الطاقة/كجم: \",\n                                                                    feedComposition.energy,\n                                                                    \" ميجا كالوري\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"الكالسيوم: \",\n                                                                    feedComposition.calcium,\n                                                                    \" جم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"الفوسفور: \",\n                                                                    feedComposition.phosphorus,\n                                                                    \" جم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: calculateRequirements,\n                                        className: \"button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold transition-colors duration-300\",\n                                        children: \"حساب الاحتياجات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: composeFeed,\n                                        disabled: !requirements,\n                                        className: `button-3d px-8 py-3 rounded-lg font-bold transition-colors duration-300 ${requirements ? \"bg-blue-200 hover:bg-blue-300\" : \"bg-gray-200 cursor-not-allowed opacity-50\"}`,\n                                        children: \"تركيب العليقة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: printResults,\n                                        disabled: !requirements,\n                                        className: `button-3d px-8 py-3 rounded-lg font-bold transition-colors duration-300 ${requirements ? \"bg-yellow-200 hover:bg-yellow-300\" : \"bg-gray-200 cursor-not-allowed opacity-50\"}`,\n                                        children: \"طباعة النتائج\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/feed-composition/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"09bb1e9faddc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzIxZmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOWJiMWU5ZmFkZGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/feed-composition/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/feed-composition/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\alaf\src\app\feed-composition\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"برنامج التغذية التطبيقية للأبقار\",\n    description: \"برنامج تكوين الأعلاف للأبقار\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO2tCQUNsQiw0RUFBQ0M7c0JBQU1KOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfYqNix2YbYp9mF2Kwg2KfZhNiq2LrYsNmK2Kkg2KfZhNiq2LfYqNmK2YLZitipINmE2YTYo9io2YLYp9ixJyxcbiAgZGVzY3JpcHRpb246ICfYqNix2YbYp9mF2Kwg2KrZg9mI2YrZhiDYp9mE2KPYudmE2KfZgSDZhNmE2KPYqNmC2KfYsScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImRpciIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffeed-composition%2Fpage&page=%2Ffeed-composition%2Fpage&appPaths=%2Ffeed-composition%2Fpage&pagePath=private-next-app-dir%2Ffeed-composition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();