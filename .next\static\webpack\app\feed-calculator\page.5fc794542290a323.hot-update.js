"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed-calculator/page",{

/***/ "(app-pages-browser)/./src/app/feed-calculator/page.tsx":
/*!******************************************!*\
  !*** ./src/app/feed-calculator/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FeedCalculator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction FeedCalculator() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const printRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedFeed, setSelectedFeed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Animal requirements\n    const [animalWeight, setAnimalWeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(500);\n    const [milkProduction, setMilkProduction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [productionStage, setProductionStage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ذروة الإنتاج\");\n    // Feed data - moved before useState\n    const feedData = [\n        {\n            name: \"الذرة الصفراء\",\n            protein: 8.5,\n            energy: 3200,\n            fiber: 2.1,\n            price: 320\n        },\n        {\n            name: \"الشعير\",\n            protein: 11.5,\n            energy: 2800,\n            fiber: 5.2,\n            price: 270\n        },\n        {\n            name: \"كسبة فول الصويا\",\n            protein: 44.0,\n            energy: 2200,\n            fiber: 7.0,\n            price: 600\n        },\n        {\n            name: \"البرسيم الحجازي\",\n            protein: 18.0,\n            energy: 2400,\n            fiber: 25.0,\n            price: 230\n        },\n        {\n            name: \"التبن\",\n            protein: 4.0,\n            energy: 1800,\n            fiber: 40.0,\n            price: 110\n        },\n        {\n            name: \"النخالة\",\n            protein: 15.5,\n            energy: 2000,\n            fiber: 12.0,\n            price: 200\n        },\n        {\n            name: \"الدريس\",\n            protein: 12.0,\n            energy: 2100,\n            fiber: 28.0,\n            price: 140\n        },\n        {\n            name: \"السيلاج\",\n            protein: 8.0,\n            energy: 2300,\n            fiber: 22.0,\n            price: 130\n        }\n    ];\n    const [showPriceEditor, setShowPriceEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editableFeedData, setEditableFeedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(feedData);\n    // Ration composition\n    const [ration, setRation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Array(8).fill(null).map(()=>({\n            feedName: \"\",\n            quantity: 0,\n            protein: 0,\n            energy: 0,\n            cost: 0\n        })));\n    // Calculate requirements based on animal data\n    const calculateRequirements = ()=>{\n        // التأكد من أن القيم موجبة\n        const weight = Math.max(animalWeight || 0, 0);\n        const milk = Math.max(milkProduction || 0, 0);\n        const baseProtein = weight * 0.004 + milk * 0.08 // كجم بروتين\n        ;\n        const baseEnergy = weight * 0.05 + milk * 1.2 // ميجا كالوري\n        ;\n        const baseDryMatter = weight * 0.03 + milk * 0.4 // كجم مادة جافة\n        ;\n        const stageMultiplier = {\n            \"بداية الإدرار\": 1.2,\n            \"ذروة الإنتاج\": 1.3,\n            \"منتصف الإدرار\": 1.1,\n            \"نهاية الإدرار\": 0.9\n        }[productionStage] || 1.1;\n        return {\n            protein: Math.max(baseProtein * stageMultiplier, 0),\n            energy: Math.max(baseEnergy * stageMultiplier, 0),\n            dryMatter: Math.max(baseDryMatter * stageMultiplier, 0)\n        };\n    };\n    const requirements = calculateRequirements();\n    // Load saved prices on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedPrices = localStorage.getItem(\"customFeedPrices\");\n        if (savedPrices) {\n            try {\n                setEditableFeedData(JSON.parse(savedPrices));\n            } catch (error) {\n                console.error(\"Error loading saved prices:\", error);\n                setEditableFeedData(feedData);\n            }\n        }\n    }, []);\n    // Update ration calculations\n    const updateRationRow = (index, field, value)=>{\n        const newRation = [\n            ...ration\n        ];\n        if (field === \"feedName\") {\n            const selectedFeedData = editableFeedData.find((f)=>f.name === value);\n            newRation[index] = {\n                ...newRation[index],\n                feedName: value,\n                protein: selectedFeedData ? newRation[index].quantity * selectedFeedData.protein / 100 : 0,\n                energy: selectedFeedData ? newRation[index].quantity * selectedFeedData.energy / 1000 : 0,\n                cost: selectedFeedData ? newRation[index].quantity * selectedFeedData.price / 1000 : 0\n            };\n        } else if (field === \"quantity\") {\n            const selectedFeedData = editableFeedData.find((f)=>f.name === newRation[index].feedName);\n            const qty = Number(value) || 0;\n            newRation[index] = {\n                ...newRation[index],\n                quantity: qty,\n                protein: selectedFeedData ? qty * selectedFeedData.protein / 100 : 0,\n                energy: selectedFeedData ? qty * selectedFeedData.energy / 1000 : 0,\n                cost: selectedFeedData ? qty * selectedFeedData.price / 1000 : 0\n            };\n        }\n        setRation(newRation);\n    };\n    // Add new row\n    const addNewRow = ()=>{\n        setRation([\n            ...ration,\n            {\n                feedName: \"\",\n                quantity: 0,\n                protein: 0,\n                energy: 0,\n                cost: 0\n            }\n        ]);\n    };\n    // Delete row\n    const deleteRow = (index)=>{\n        if (ration.length > 1) {\n            const newRation = ration.filter((_, i)=>i !== index);\n            setRation(newRation);\n        } else {\n            // Reset the row if it's the last one\n            const newRation = [\n                ...ration\n            ];\n            newRation[index] = {\n                feedName: \"\",\n                quantity: 0,\n                protein: 0,\n                energy: 0,\n                cost: 0\n            };\n            setRation(newRation);\n        }\n    };\n    // Clear all rows\n    const clearAllRows = ()=>{\n        setRation(Array(5).fill(null).map(()=>({\n                feedName: \"\",\n                quantity: 0,\n                protein: 0,\n                energy: 0,\n                cost: 0\n            })));\n    };\n    // Update feed price\n    const updateFeedPrice = (feedName, newPrice)=>{\n        const updatedFeedData = editableFeedData.map((feed)=>feed.name === feedName ? {\n                ...feed,\n                price: newPrice\n            } : feed);\n        setEditableFeedData(updatedFeedData);\n        // Update existing ration calculations\n        const updatedRation = ration.map((row)=>{\n            if (row.feedName === feedName) {\n                const updatedFeed = updatedFeedData.find((f)=>f.name === feedName);\n                return {\n                    ...row,\n                    cost: updatedFeed ? row.quantity * updatedFeed.price / 1000 : row.cost\n                };\n            }\n            return row;\n        });\n        setRation(updatedRation);\n    };\n    // Save prices to localStorage\n    const savePrices = ()=>{\n        try {\n            localStorage.setItem(\"customFeedPrices\", JSON.stringify(editableFeedData));\n            alert(\"تم حفظ الأسعار بنجاح!\");\n        } catch (error) {\n            console.error(\"Error saving prices:\", error);\n            alert(\"حدث خطأ أثناء حفظ الأسعار!\");\n        }\n    };\n    // Load prices from localStorage\n    const loadPrices = ()=>{\n        try {\n            const savedPrices = localStorage.getItem(\"customFeedPrices\");\n            if (savedPrices) {\n                setEditableFeedData(JSON.parse(savedPrices));\n                alert(\"تم تحميل الأسعار المحفوظة!\");\n            } else {\n                alert(\"لا توجد أسعار محفوظة!\");\n            }\n        } catch (error) {\n            console.error(\"Error loading prices:\", error);\n            alert(\"حدث خطأ أثناء تحميل الأسعار!\");\n        }\n    };\n    // Reset prices to default\n    const resetPrices = ()=>{\n        setEditableFeedData([\n            ...feedData\n        ]);\n        localStorage.removeItem(\"customFeedPrices\");\n        alert(\"تم إعادة تعيين الأسعار للقيم الافتراضية!\");\n    };\n    // Calculate totals\n    const totals = ration.reduce((acc, row)=>({\n            quantity: acc.quantity + row.quantity,\n            protein: acc.protein + row.protein,\n            energy: acc.energy + row.energy,\n            cost: acc.cost + row.cost\n        }), {\n        quantity: 0,\n        protein: 0,\n        energy: 0,\n        cost: 0\n    });\n    // Print function\n    const handlePrint = ()=>{\n        const printContent = printRef.current;\n        if (printContent) {\n            const printWindow = window.open(\"\", \"_blank\");\n            if (printWindow) {\n                printWindow.document.write(\"\\n          <html>\\n            <head>\\n              <title>تقرير تكوين العليقة</title>\\n              <style>\\n                body { font-family: Arial, sans-serif; direction: rtl; }\\n                table { width: 100%; border-collapse: collapse; margin: 20px 0; }\\n                th, td { border: 1px solid #000; padding: 8px; text-align: center; }\\n                th { background-color: #f0f0f0; }\\n                .header { text-align: center; margin-bottom: 30px; }\\n                .summary { margin: 20px 0; }\\n              </style>\\n            </head>\\n            <body>\\n              \".concat(printContent.innerHTML, \"\\n            </body>\\n          </html>\\n        \"));\n                printWindow.document.close();\n                printWindow.print();\n            }\n        }\n    };\n    // Save ration\n    const saveRation = ()=>{\n        const rationData = {\n            animalWeight,\n            milkProduction,\n            productionStage,\n            ration: ration.filter((row)=>row.feedName && row.quantity > 0),\n            totals,\n            requirements,\n            date: new Date().toLocaleDateString(\"ar-SA\")\n        };\n        localStorage.setItem(\"savedRation\", JSON.stringify(rationData));\n        alert(\"تم حفظ العليقة بنجاح!\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-4 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.back(),\n                                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                children: \"← العودة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-center text-blue-800\",\n                                children: \"دراسة الجدوى الاقتصادية - برنامج تكوين العلائق\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: printRef,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"header\",\n                            style: {\n                                display: \"none\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    children: \"تقرير تكوين العليقة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"التاريخ: \",\n                                        new Date().toLocaleDateString(\"ar-SA\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"وزن الحيوان: \",\n                                        animalWeight,\n                                        \" كجم | إنتاج الحليب: \",\n                                        milkProduction,\n                                        \" لتر\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-12 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-600 text-white p-3 rounded-t-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-center font-bold\",\n                                                    children: \"مواد العلف المتاحة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-sm\",\n                                                    children: \"ترتيب حسب التكلفة المنخفضة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-300 rounded-b-lg\",\n                                            children: editableFeedData.map((feed, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 border-b border-gray-200 cursor-pointer hover:bg-blue-50 \".concat(selectedFeed === feed.name ? \"bg-blue-100\" : \"\"),\n                                                    onClick: ()=>setSelectedFeed(feed.name),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: feed.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: [\n                                                                        feed.price,\n                                                                        \" دينار\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"بروتين: \",\n                                                                feed.protein,\n                                                                \"% | طاقة: \",\n                                                                feed.energy\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-600 text-white p-3 rounded-t-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-center font-bold\",\n                                                children: \"جدول حساب تكوين العليقة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-gray-300 rounded-b-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"bg-gray-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"المادة العلفية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"الكمية (كجم)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"البروتين\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"الطاقة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"التكلفة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"border border-gray-300 p-2\",\n                                                                    children: \"الإجراءات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: [\n                                                            ration.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                className: \"w-full text-xs\",\n                                                                                value: row.feedName,\n                                                                                onChange: (e)=>updateRationRow(index, \"feedName\", e.target.value),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"\",\n                                                                                        children: \"اختر المادة\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                        lineNumber: 355,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    editableFeedData.map((feed, feedIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: feed.name,\n                                                                                            children: feed.name\n                                                                                        }, feedIndex, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                            lineNumber: 357,\n                                                                                            columnNumber: 29\n                                                                                        }, this))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"number\",\n                                                                                className: \"w-full text-xs p-1\",\n                                                                                placeholder: \"0\",\n                                                                                value: row.quantity || \"\",\n                                                                                onChange: (e)=>updateRationRow(index, \"quantity\", e.target.value)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2 text-center\",\n                                                                            children: row.protein > 0 ? row.protein.toFixed(2) : \"--\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2 text-center\",\n                                                                            children: row.energy > 0 ? row.energy.toFixed(1) : \"--\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2 text-center\",\n                                                                            children: row.cost > 0 ? row.cost.toFixed(2) : \"--\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 p-2 text-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-1 justify-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>deleteRow(index),\n                                                                                        className: \"px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600\",\n                                                                                        title: \"حذف الصف\",\n                                                                                        children: \"\\uD83D\\uDDD1️\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                        lineNumber: 381,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            // Duplicate row\n                                                                                            const newRation = [\n                                                                                                ...ration\n                                                                                            ];\n                                                                                            newRation.splice(index + 1, 0, {\n                                                                                                ...row\n                                                                                            });\n                                                                                            setRation(newRation);\n                                                                                        },\n                                                                                        className: \"px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600\",\n                                                                                        title: \"نسخ الصف\",\n                                                                                        children: \"\\uD83D\\uDCCB\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                        lineNumber: 388,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 380,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 21\n                                                                }, this)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"bg-yellow-100 font-bold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2\",\n                                                                        children: \"المجموع\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: totals.quantity.toFixed(1)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: totals.protein.toFixed(2)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: totals.energy.toFixed(1)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: totals.cost.toFixed(2)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"border border-gray-300 p-2 text-center\",\n                                                                        children: \"--\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-700 p-3 rounded-b-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center gap-2 flex-wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: addNewRow,\n                                                        className: \"px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600\",\n                                                        title: \"إضافة صف جديد\",\n                                                        children: \"➕ إضافة صف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: clearAllRows,\n                                                        className: \"px-3 py-2 bg-red-500 text-white rounded text-sm hover:bg-red-600\",\n                                                        title: \"مسح جميع الصفوف\",\n                                                        children: \"\\uD83D\\uDDD1️ مسح الكل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            // Auto calculate - already happening in real time\n                                                            alert(\"تم حساب التكوين تلقائياً!\");\n                                                        },\n                                                        className: \"px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\",\n                                                        children: \"\\uD83D\\uDCCA حساب التكوين\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: saveRation,\n                                                        className: \"px-3 py-2 bg-purple-500 text-white rounded text-sm hover:bg-purple-600\",\n                                                        children: \"\\uD83D\\uDCBE حفظ العليقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handlePrint,\n                                                        className: \"px-3 py-2 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600\",\n                                                        children: \"\\uD83D\\uDDA8️ طباعة النتائج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            const savedRation = localStorage.getItem(\"savedRation\");\n                                                            if (savedRation) {\n                                                                const rationData = JSON.parse(savedRation);\n                                                                setRation(rationData.ration || rationData);\n                                                                if (rationData.animalWeight) setAnimalWeight(rationData.animalWeight);\n                                                                if (rationData.milkProduction) setMilkProduction(rationData.milkProduction);\n                                                                if (rationData.productionStage) setProductionStage(rationData.productionStage);\n                                                                alert(\"تم تحميل العليقة المحفوظة!\");\n                                                            } else {\n                                                                alert(\"لا توجد عليقة محفوظة!\");\n                                                            }\n                                                        },\n                                                        className: \"px-3 py-2 bg-orange-500 text-white rounded text-sm hover:bg-orange-600\",\n                                                        children: \"\\uD83D\\uDCC2 تحميل العليقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowPriceEditor(true),\n                                                        className: \"px-3 py-2 bg-indigo-500 text-white rounded text-sm hover:bg-indigo-600\",\n                                                        children: \"\\uD83D\\uDCB0 تعديل الأسعار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 text-white p-3 rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-center font-bold text-sm\",\n                                                        children: \"احتياجات الحيوان\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-300 rounded-b-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-bold mb-1\",\n                                                                        children: \"وزن الحيوان (كجم)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        className: \"w-full p-2 border rounded\",\n                                                                        value: animalWeight,\n                                                                        onChange: (e)=>setAnimalWeight(Number(e.target.value) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-bold mb-1\",\n                                                                        children: \"إنتاج الحليب (لتر)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        className: \"w-full p-2 border rounded\",\n                                                                        value: milkProduction,\n                                                                        onChange: (e)=>setMilkProduction(Number(e.target.value) || 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-bold mb-1\",\n                                                                        children: \"مرحلة الإنتاج\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 520,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        className: \"w-full p-2 border rounded text-sm\",\n                                                                        value: productionStage,\n                                                                        onChange: (e)=>setProductionStage(e.target.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"بداية الإدرار\",\n                                                                                children: \"بداية الإدرار\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"ذروة الإنتاج\",\n                                                                                children: \"ذروة الإنتاج\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"منتصف الإدرار\",\n                                                                                children: \"منتصف الإدرار\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 528,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"نهاية الإدرار\",\n                                                                                children: \"نهاية الإدرار\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-orange-600 text-white p-3 rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-center font-bold text-sm\",\n                                                        children: \"التحليل الغذائي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-300 rounded-b-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"البروتين المطلوب:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            requirements.protein.toFixed(2),\n                                                                            \" كجم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"الطاقة المطلوبة:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            requirements.energy.toFixed(1),\n                                                                            \" ميجا كالوري\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"المادة الجافة:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            requirements.dryMatter.toFixed(1),\n                                                                            \" كجم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-green-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"البروتين المحسوب:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            totals.protein.toFixed(2),\n                                                                            \" كجم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-green-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"الطاقة المحسوبة:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            totals.energy.toFixed(1),\n                                                                            \" ميجا كالوري\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"نسبة تحقق البروتين:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold \".concat(totals.protein >= requirements.protein * 0.95 ? \"text-green-600\" : \"text-red-600\"),\n                                                                        children: [\n                                                                            requirements.protein > 0 ? (totals.protein / requirements.protein * 100).toFixed(1) : 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"نسبة تحقق الطاقة:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold \".concat(totals.energy >= requirements.energy * 0.95 ? \"text-green-600\" : \"text-red-600\"),\n                                                                        children: [\n                                                                            requirements.energy > 0 ? (totals.energy / requirements.energy * 100).toFixed(1) : 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-purple-600 text-white p-3 rounded-t-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-center font-bold text-sm\",\n                                                        children: \"تحليل التكلفة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white border border-gray-300 rounded-b-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"تكلفة العليقة اليومية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            totals.cost.toFixed(2),\n                                                                            \" دينار\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"تكلفة شهرية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 597,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            (totals.cost * 30).toFixed(2),\n                                                                            \" دينار\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"تكلفة سنوية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            (totals.cost * 365).toFixed(2),\n                                                                            \" دينار\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-blue-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"تكلفة لتر الحليب:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            milkProduction > 0 ? (totals.cost / milkProduction).toFixed(2) : \"0.00\",\n                                                                            \" دينار\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-purple-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"الربحية (إذا كان سعر اللتر 2.1 دينار):\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold\",\n                                                                        children: [\n                                                                            milkProduction > 0 ? ((2.1 - totals.cost / milkProduction) * milkProduction).toFixed(2) : \"0.00\",\n                                                                            \" دينار/يوم\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this),\n                showPriceEditor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-blue-700\",\n                                        children: \"تعديل أسعار المواد العلفية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPriceEditor(false),\n                                        className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: editableFeedData.map((feed, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-gray-800\",\n                                                        children: feed.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"بروتين: \",\n                                                            feed.protein,\n                                                            \"% | طاقة: \",\n                                                            feed.energy,\n                                                            \" كالوري | ألياف: \",\n                                                            feed.fiber,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: feed.price,\n                                                        onChange: (e)=>updateFeedPrice(feed.name, Number(e.target.value) || 0),\n                                                        className: \"w-24 p-2 border rounded text-center\",\n                                                        min: \"0\",\n                                                        step: \"10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"دينار/طن\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: savePrices,\n                                        className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n                                        children: \"\\uD83D\\uDCBE حفظ الأسعار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: loadPrices,\n                                        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                        children: \"\\uD83D\\uDCC2 تحميل الأسعار\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetPrices,\n                                        className: \"px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600\",\n                                        children: \"\\uD83D\\uDD04 إعادة تعيين\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPriceEditor(false),\n                                        className: \"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600\",\n                                        children: \"إغلاق\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-calculator\\\\page.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedCalculator, \"e12M4rJHfL6/TFh4JsDFOiJXqWk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = FeedCalculator;\nvar _c;\n$RefreshReg$(_c, \"FeedCalculator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed-calculator/page.tsx\n"));

/***/ })

});