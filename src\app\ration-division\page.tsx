'use client'

import { useState } from 'react'
import PageTemplate from '@/components/PageTemplate'

interface AnimalGroup {
  id: number
  name: string
  production: string
  count: number
  concentrate: string
  roughage: string
  protein: string
  energy: string
}

export default function RationDivision() {
  const [selectedGroup, setSelectedGroup] = useState<number | null>(null)
  const [animalCounts, setAnimalCounts] = useState({
    group1: 0,
    group2: 0,
    group3: 0
  })
  const [showCalculation, setShowCalculation] = useState(false)

  const groups: AnimalGroup[] = [
    {
      id: 1,
      name: 'المجموعة الأولى',
      production: 'أبقار عالية الإنتاج (أكثر من 30 لتر/يوم)',
      count: animalCounts.group1,
      concentrate: '12-15 كجم',
      roughage: '8-10 كجم',
      protein: '18-20%',
      energy: '1.7-1.9 ميجا كالوري'
    },
    {
      id: 2,
      name: 'المجموعة الثانية',
      production: 'أبقار متوسطة الإنتاج (20-30 لتر/يوم)',
      count: animalCounts.group2,
      concentrate: '8-12 كجم',
      roughage: '10-12 كجم',
      protein: '16-18%',
      energy: '1.5-1.7 ميجا كالوري'
    },
    {
      id: 3,
      name: 'المجموعة الثالثة',
      production: 'أبقار منخفضة الإنتاج (أقل من 20 لتر/يوم)',
      count: animalCounts.group3,
      concentrate: '5-8 كجم',
      roughage: '12-15 كجم',
      protein: '14-16%',
      energy: '1.3-1.5 ميجا كالوري'
    }
  ]

  const handleGroupClassification = () => {
    setSelectedGroup(null)
    setShowCalculation(false)
    // Show classification form
    alert('سيتم فتح نموذج تصنيف القطيع')
  }

  const handleQuantityCalculation = () => {
    if (animalCounts.group1 === 0 && animalCounts.group2 === 0 && animalCounts.group3 === 0) {
      alert('يرجى إدخال عدد الحيوانات في كل مجموعة أولاً')
      return
    }
    setShowCalculation(true)
  }

  const updateAnimalCount = (group: string, count: number) => {
    setAnimalCounts(prev => ({
      ...prev,
      [group]: Math.max(0, count)
    }))
  }

  const calculateTotalFeed = (group: AnimalGroup) => {
    const concentrateMin = parseInt(group.concentrate.split('-')[0])
    const concentrateMax = parseInt(group.concentrate.split('-')[1])
    const roughageMin = parseInt(group.roughage.split('-')[0])
    const roughageMax = parseInt(group.roughage.split('-')[1])

    const concentrateAvg = (concentrateMin + concentrateMax) / 2
    const roughageAvg = (roughageMin + roughageMax) / 2

    return {
      concentrate: (concentrateAvg * group.count).toFixed(1),
      roughage: (roughageAvg * group.count).toFixed(1),
      total: ((concentrateAvg + roughageAvg) * group.count).toFixed(1)
    }
  }

  return (
    <PageTemplate title="قسمة العلائق">
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-center text-green-700 mb-6">
          نظام تقسيم العلائق حسب المجموعات
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {groups.map((group, index) => (
            <div
              key={group.id}
              className={`p-6 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                index === 0 ? 'bg-blue-50 border-blue-200 hover:bg-blue-100' :
                index === 1 ? 'bg-green-50 border-green-200 hover:bg-green-100' :
                'bg-yellow-50 border-yellow-200 hover:bg-yellow-100'
              } ${selectedGroup === group.id ? 'ring-4 ring-blue-300' : ''}`}
              onClick={() => setSelectedGroup(selectedGroup === group.id ? null : group.id)}
            >
              <h3 className={`text-xl font-bold mb-4 ${
                index === 0 ? 'text-blue-700' :
                index === 1 ? 'text-green-700' :
                'text-yellow-700'
              }`}>
                {group.name}
              </h3>
              <p className="text-sm mb-3">{group.production}</p>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">عدد الحيوانات:</label>
                <input
                  type="number"
                  min="0"
                  value={group.count}
                  onChange={(e) => updateAnimalCount(`group${group.id}`, parseInt(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0"
                />
              </div>

              <ul className="space-y-2 text-sm">
                <li>• علف مركز: {group.concentrate}</li>
                <li>• علف خشن: {group.roughage}</li>
                <li>• بروتين: {group.protein}</li>
                <li>• طاقة: {group.energy}</li>
              </ul>

              {group.count > 0 && showCalculation && (
                <div className="mt-4 p-3 bg-white rounded border">
                  <h4 className="font-bold text-sm mb-2">الكميات المطلوبة يومياً:</h4>
                  <div className="text-xs space-y-1">
                    <p>علف مركز: {calculateTotalFeed(group).concentrate} كجم</p>
                    <p>علف خشن: {calculateTotalFeed(group).roughage} كجم</p>
                    <p className="font-bold">المجموع: {calculateTotalFeed(group).total} كجم</p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-center gap-4 mt-8">
          <button
            onClick={handleGroupClassification}
            className="button-3d px-8 py-3 rounded-lg bg-blue-200 hover:bg-blue-300 font-bold transition-colors duration-300"
          >
            تصنيف القطيع
          </button>
          <button
            onClick={handleQuantityCalculation}
            className="button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold transition-colors duration-300"
          >
            حساب الكميات
          </button>
        </div>

        {showCalculation && (animalCounts.group1 > 0 || animalCounts.group2 > 0 || animalCounts.group3 > 0) && (
          <div className="mt-8 p-6 bg-gray-50 rounded-lg border-2 border-gray-200">
            <h3 className="text-xl font-bold text-center text-gray-700 mb-4">
              ملخص الكميات المطلوبة يومياً
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded border">
                <h4 className="font-bold text-blue-600 mb-2">إجمالي العلف المركز:</h4>
                <p className="text-2xl font-bold text-blue-700">
                  {groups.reduce((total, group) => total + parseFloat(calculateTotalFeed(group).concentrate), 0).toFixed(1)} كجم
                </p>
              </div>
              <div className="bg-white p-4 rounded border">
                <h4 className="font-bold text-green-600 mb-2">إجمالي العلف الخشن:</h4>
                <p className="text-2xl font-bold text-green-700">
                  {groups.reduce((total, group) => total + parseFloat(calculateTotalFeed(group).roughage), 0).toFixed(1)} كجم
                </p>
              </div>
            </div>
            <div className="mt-4 text-center">
              <div className="bg-white p-4 rounded border">
                <h4 className="font-bold text-gray-600 mb-2">إجمالي العلف المطلوب:</h4>
                <p className="text-3xl font-bold text-gray-700">
                  {groups.reduce((total, group) => total + parseFloat(calculateTotalFeed(group).total), 0).toFixed(1)} كجم/يوم
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </PageTemplate>
  )
}
