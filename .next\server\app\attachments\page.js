/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/attachments/page";
exports.ids = ["app/attachments/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fattachments%2Fpage&page=%2Fattachments%2Fpage&appPaths=%2Fattachments%2Fpage&pagePath=private-next-app-dir%2Fattachments%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fattachments%2Fpage&page=%2Fattachments%2Fpage&appPaths=%2Fattachments%2Fpage&pagePath=private-next-app-dir%2Fattachments%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'attachments',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/attachments/page.tsx */ \"(rsc)/./src/app/attachments/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/attachments/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/attachments/page\",\n        pathname: \"/attachments\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fattachments%2Fpage&page=%2Fattachments%2Fpage&appPaths=%2Fattachments%2Fpage&pagePath=private-next-app-dir%2Fattachments%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cattachments%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cattachments%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/attachments/page.tsx */ \"(ssr)/./src/app/attachments/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNzcmMlNUNhcHAlNUNhdHRhY2htZW50cyU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ZlZWQtZm9ybXVsYXRpb24tYXBwLz82OWM2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccXVyYWFuXFxcXERlc2t0b3BcXFxcYWxhZlxcXFxzcmNcXFxcYXBwXFxcXGF0dGFjaG1lbnRzXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cattachments%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/attachments/page.tsx":
/*!**************************************!*\
  !*** ./src/app/attachments/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Attachments)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Attachments() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"جداول القيم الغذائية\",\n            type: \"PDF\",\n            size: \"2.5 MB\",\n            description: \"جداول شاملة للقيم الغذائية لجميع مواد العلف\",\n            downloadCount: 245,\n            uploadDate: \"2024-01-15\",\n            category: \"جداول\"\n        },\n        {\n            id: 2,\n            name: \"دليل تركيب العلائق\",\n            type: \"PDF\",\n            size: \"1.8 MB\",\n            description: \"دليل مفصل لتركيب العلائق المتوازنة للحيوانات\",\n            downloadCount: 189,\n            uploadDate: \"2024-01-10\",\n            category: \"أدلة\"\n        },\n        {\n            id: 3,\n            name: \"صور مواد العلف\",\n            type: \"ZIP\",\n            size: \"15.2 MB\",\n            description: \"مجموعة صور عالية الجودة لمختلف مواد العلف\",\n            downloadCount: 156,\n            uploadDate: \"2024-01-08\",\n            category: \"صور\"\n        },\n        {\n            id: 4,\n            name: \"نماذج حساب الاحتياجات\",\n            type: \"Excel\",\n            size: \"850 KB\",\n            description: \"نماذج Excel لحساب الاحتياجات الغذائية للحيوانات\",\n            downloadCount: 312,\n            uploadDate: \"2024-01-05\",\n            category: \"نماذج\"\n        },\n        {\n            id: 5,\n            name: \"فيديوهات تعليمية\",\n            type: \"MP4\",\n            size: \"125 MB\",\n            description: \"مقاطع فيديو تعليمية لتركيب العلائق\",\n            downloadCount: 98,\n            uploadDate: \"2024-01-01\",\n            category: \"فيديو\"\n        }\n    ]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الكل\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"name\");\n    const [showUploadForm, setShowUploadForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFile, setNewFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        type: \"\",\n        size: \"\",\n        description: \"\",\n        category: \"\"\n    });\n    const categories = [\n        \"الكل\",\n        \"جداول\",\n        \"أدلة\",\n        \"صور\",\n        \"نماذج\",\n        \"فيديو\"\n    ];\n    const filteredAttachments = attachments.filter((file)=>file.name.toLowerCase().includes(searchTerm.toLowerCase()) && (selectedCategory === \"الكل\" || file.category === selectedCategory)).sort((a, b)=>{\n        switch(sortBy){\n            case \"name\":\n                return a.name.localeCompare(b.name);\n            case \"date\":\n                return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime();\n            case \"downloads\":\n                return b.downloadCount - a.downloadCount;\n            case \"size\":\n                return parseFloat(a.size) - parseFloat(b.size);\n            default:\n                return 0;\n        }\n    });\n    const handleDownload = (file)=>{\n        // محاكاة تحميل الملف\n        setAttachments(attachments.map((att)=>att.id === file.id ? {\n                ...att,\n                downloadCount: att.downloadCount + 1\n            } : att));\n        // إنشاء رابط تحميل وهمي\n        const link = document.createElement(\"a\");\n        link.href = \"#\";\n        link.download = file.name;\n        link.click();\n        alert(`تم بدء تحميل: ${file.name}`);\n    };\n    const handleUpload = ()=>{\n        if (newFile.name && newFile.type && newFile.category) {\n            const id = Math.max(...attachments.map((att)=>att.id)) + 1;\n            const newAttachment = {\n                ...newFile,\n                id,\n                downloadCount: 0,\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                size: newFile.size || \"1.0 MB\"\n            };\n            setAttachments([\n                ...attachments,\n                newAttachment\n            ]);\n            setNewFile({\n                name: \"\",\n                type: \"\",\n                size: \"\",\n                description: \"\",\n                category: \"\"\n            });\n            setShowUploadForm(false);\n            alert(\"تم رفع الملف بنجاح!\");\n        } else {\n            alert(\"يرجى إدخال جميع البيانات المطلوبة\");\n        }\n    };\n    const handleDelete = (id)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الملف؟\")) {\n            setAttachments(attachments.filter((att)=>att.id !== id));\n        }\n    };\n    const getFileIcon = (type)=>{\n        switch(type.toLowerCase()){\n            case \"pdf\":\n                return \"\\uD83D\\uDCC4\";\n            case \"excel\":\n                return \"\\uD83D\\uDCCA\";\n            case \"zip\":\n                return \"\\uD83D\\uDCE6\";\n            case \"mp4\":\n                return \"\\uD83C\\uDFA5\";\n            default:\n                return \"\\uD83D\\uDCC1\";\n        }\n    };\n    const getTotalSize = ()=>{\n        return attachments.reduce((total, file)=>{\n            const size = parseFloat(file.size);\n            const unit = file.size.includes(\"MB\") ? 1 : file.size.includes(\"KB\") ? 0.001 : 1;\n            return total + size * unit;\n        }, 0).toFixed(1);\n    };\n    const getTotalDownloads = ()=>{\n        return attachments.reduce((total, file)=>total + file.downloadCount, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-300 via-blue-100 to-blue-300 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.back(),\n                            className: \"button-3d px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300\",\n                            children: \"← العودة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-blue-900\",\n                            children: \"الملفات والمرفقات المساعدة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wood-texture rounded-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-4 rounded-lg border-2 border-blue-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-blue-700\",\n                                                children: \"إجمالي الملفات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-900\",\n                                                children: attachments.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 p-4 rounded-lg border-2 border-green-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-green-700\",\n                                                children: \"إجمالي التحميلات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-900\",\n                                                children: getTotalDownloads()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-orange-50 p-4 rounded-lg border-2 border-orange-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-orange-700\",\n                                                children: \"إجمالي الحجم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-orange-900\",\n                                                children: [\n                                                    getTotalSize(),\n                                                    \" MB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 p-4 rounded-lg border-2 border-purple-200 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-purple-700\",\n                                                children: \"الفئات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-900\",\n                                                children: categories.length - 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"البحث في الملفات...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedCategory,\n                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                className: \"p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category,\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: \"ترتيب بالاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"date\",\n                                                        children: \"ترتيب بالتاريخ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"downloads\",\n                                                        children: \"ترتيب بالتحميلات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"size\",\n                                                        children: \"ترتيب بالحجم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUploadForm(true),\n                                        className: \"button-3d px-6 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold\",\n                                        children: \"+ إضافة ملف جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            showUploadForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-blue-50 rounded-lg border-2 border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-blue-700 mb-4\",\n                                        children: \"رفع ملف جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم الملف\",\n                                                value: newFile.name,\n                                                onChange: (e)=>setNewFile({\n                                                        ...newFile,\n                                                        name: e.target.value\n                                                    }),\n                                                className: \"p-2 border rounded focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: newFile.type,\n                                                onChange: (e)=>setNewFile({\n                                                        ...newFile,\n                                                        type: e.target.value\n                                                    }),\n                                                className: \"p-2 border rounded focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"نوع الملف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"PDF\",\n                                                        children: \"PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"Excel\",\n                                                        children: \"Excel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ZIP\",\n                                                        children: \"ZIP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"MP4\",\n                                                        children: \"MP4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"DOC\",\n                                                        children: \"DOC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"حجم الملف (مثل: 2.5 MB)\",\n                                                value: newFile.size,\n                                                onChange: (e)=>setNewFile({\n                                                        ...newFile,\n                                                        size: e.target.value\n                                                    }),\n                                                className: \"p-2 border rounded focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: newFile.category,\n                                                onChange: (e)=>setNewFile({\n                                                        ...newFile,\n                                                        category: e.target.value\n                                                    }),\n                                                className: \"p-2 border rounded focus:border-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"الفئة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    categories.slice(1).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category,\n                                                            children: category\n                                                        }, category, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف الملف\",\n                                                value: newFile.description,\n                                                onChange: (e)=>setNewFile({\n                                                        ...newFile,\n                                                        description: e.target.value\n                                                    }),\n                                                className: \"p-2 border rounded focus:border-blue-500 md:col-span-2\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpload,\n                                                className: \"button-3d px-4 py-2 rounded bg-green-200 hover:bg-green-300 font-bold\",\n                                                children: \"رفع الملف\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowUploadForm(false),\n                                                className: \"button-3d px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 font-bold\",\n                                                children: \"إلغاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\",\n                                children: filteredAttachments.map((file)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border-2 border-blue-200 hover:shadow-lg transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: getFileIcon(file.type)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-blue-800 text-sm\",\n                                                                        children: file.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: [\n                                                                            file.type,\n                                                                            \" - \",\n                                                                            file.size\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-200 text-blue-800 px-2 py-1 rounded text-xs font-bold\",\n                                                        children: file.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 mb-3 line-clamp-2\",\n                                                children: file.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-600 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"\\uD83D\\uDCC5 \",\n                                                            file.uploadDate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"⬇️ \",\n                                                            file.downloadCount,\n                                                            \" تحميل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDownload(file),\n                                                        className: \"flex-1 button-3d px-3 py-2 rounded bg-green-200 hover:bg-green-300 text-sm font-bold\",\n                                                        children: \"تحميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDelete(file.id),\n                                                        className: \"button-3d px-3 py-2 rounded bg-red-200 hover:bg-red-300 text-sm font-bold\",\n                                                        children: \"حذف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, file.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this),\n                            filteredAttachments.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"لا توجد ملفات تطابق البحث\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowUploadForm(true),\n                                        className: \"button-3d px-8 py-3 rounded-lg bg-blue-200 hover:bg-blue-300 font-bold transition-colors duration-300\",\n                                        children: \"إضافة ملف جديد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.reload(),\n                                        className: \"button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold transition-colors duration-300\",\n                                        children: \"تحديث المرفقات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            const totalFiles = attachments.length;\n                                            const totalSize = getTotalSize();\n                                            const totalDownloads = getTotalDownloads();\n                                            alert(`إحصائيات المرفقات:\\n\\nإجمالي الملفات: ${totalFiles}\\nإجمالي الحجم: ${totalSize} MB\\nإجمالي التحميلات: ${totalDownloads}`);\n                                        },\n                                        className: \"button-3d px-8 py-3 rounded-lg bg-yellow-200 hover:bg-yellow-300 font-bold transition-colors duration-300\",\n                                        children: \"عرض الإحصائيات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 bg-yellow-50 p-4 rounded-lg border-2 border-yellow-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-yellow-800 mb-2\",\n                                        children: \"تعليمات الاستخدام:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-yellow-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• استخدم شريط البحث للعثور على ملفات محددة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• اختر فئة معينة لتصفية الملفات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• يمكنك ترتيب الملفات حسب الاسم أو التاريخ أو عدد التحميلات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: '• اضغط \"تحميل\" لتنزيل الملف (سيزيد عداد التحميلات)'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• يمكنك إضافة ملفات جديدة باستخدام نموذج الرفع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/attachments/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"09bb1e9faddc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzIxZmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOWJiMWU5ZmFkZGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/attachments/page.tsx":
/*!**************************************!*\
  !*** ./src/app/attachments/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\alaf\src\app\attachments\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"برنامج التغذية التطبيقية للأبقار\",\n    description: \"برنامج تكوين الأعلاف للأبقار\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO2tCQUNsQiw0RUFBQ0M7c0JBQU1KOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfYqNix2YbYp9mF2Kwg2KfZhNiq2LrYsNmK2Kkg2KfZhNiq2LfYqNmK2YLZitipINmE2YTYo9io2YLYp9ixJyxcbiAgZGVzY3JpcHRpb246ICfYqNix2YbYp9mF2Kwg2KrZg9mI2YrZhiDYp9mE2KPYudmE2KfZgSDZhNmE2KPYqNmC2KfYsScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImRpciIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fattachments%2Fpage&page=%2Fattachments%2Fpage&appPaths=%2Fattachments%2Fpage&pagePath=private-next-app-dir%2Fattachments%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();