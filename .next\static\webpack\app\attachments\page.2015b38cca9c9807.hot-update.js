/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/attachments/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cattachments%5Cpage.tsx&server=false!":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cattachments%5Cpage.tsx&server=false! ***!
  \*******************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/attachments/page.tsx */ \"(app-pages-browser)/./src/app/attachments/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNxdXJhYW4lNUNEZXNrdG9wJTVDYWxhZiU1Q3NyYyU1Q2FwcCU1Q2F0dGFjaG1lbnRzJTVDcGFnZS50c3gmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzczY2YiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxxdXJhYW5cXFxcRGVza3RvcFxcXFxhbGFmXFxcXHNyY1xcXFxhcHBcXFxcYXR0YWNobWVudHNcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cattachments%5Cpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/attachments/page.tsx":
/*!**************************************!*\
  !*** ./src/app/attachments/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Attachments; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Attachments() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"جداول القيم الغذائية\",\n            type: \"PDF\",\n            size: \"2.5 MB\",\n            description: \"جداول شاملة للقيم الغذائية لجميع مواد العلف\",\n            downloadCount: 245,\n            uploadDate: \"2024-01-15\",\n            category: \"جداول\"\n        },\n        {\n            id: 2,\n            name: \"دليل تركيب العلائق\",\n            type: \"PDF\",\n            size: \"1.8 MB\",\n            description: \"دليل مفصل لتركيب العلائق المتوازنة للحيوانات\",\n            downloadCount: 189,\n            uploadDate: \"2024-01-10\",\n            category: \"أدلة\"\n        },\n        {\n            id: 3,\n            name: \"صور مواد العلف\",\n            type: \"ZIP\",\n            size: \"15.2 MB\",\n            description: \"مجموعة صور عالية الجودة لمختلف مواد العلف\",\n            downloadCount: 156,\n            uploadDate: \"2024-01-08\",\n            category: \"صور\"\n        },\n        {\n            id: 4,\n            name: \"نماذج حساب الاحتياجات\",\n            type: \"Excel\",\n            size: \"850 KB\",\n            description: \"نماذج Excel لحساب الاحتياجات الغذائية للحيوانات\",\n            downloadCount: 312,\n            uploadDate: \"2024-01-05\",\n            category: \"نماذج\"\n        },\n        {\n            id: 5,\n            name: \"فيديوهات تعليمية\",\n            type: \"MP4\",\n            size: \"125 MB\",\n            description: \"مقاطع فيديو تعليمية لتركيب العلائق\",\n            downloadCount: 98,\n            uploadDate: \"2024-01-01\",\n            category: \"فيديو\"\n        }\n    ]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"الكل\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"name\");\n    const [showUploadForm, setShowUploadForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newFile, setNewFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        type: \"\",\n        size: \"\",\n        description: \"\",\n        category: \"\"\n    });\n    const categories = [\n        \"الكل\",\n        \"جداول\",\n        \"أدلة\",\n        \"صور\",\n        \"نماذج\",\n        \"فيديو\"\n    ];\n    const filteredAttachments = attachments.filter((file)=>file.name.toLowerCase().includes(searchTerm.toLowerCase()) && (selectedCategory === \"الكل\" || file.category === selectedCategory)).sort((a, b)=>{\n        switch(sortBy){\n            case \"name\":\n                return a.name.localeCompare(b.name);\n            case \"date\":\n                return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime();\n            case \"downloads\":\n                return b.downloadCount - a.downloadCount;\n            case \"size\":\n                return parseFloat(a.size) - parseFloat(b.size);\n            default:\n                return 0;\n        }\n    });\n    const handleDownload = (file)=>{\n        // محاكاة تحميل الملف\n        setAttachments(attachments.map((att)=>att.id === file.id ? {\n                ...att,\n                downloadCount: att.downloadCount + 1\n            } : att));\n        // إنشاء رابط تحميل وهمي\n        const link = document.createElement(\"a\");\n        link.href = \"#\";\n        link.download = file.name;\n        link.click();\n        alert(\"تم بدء تحميل: \".concat(file.name));\n    };\n    const handleUpload = ()=>{\n        if (newFile.name && newFile.type && newFile.category) {\n            const id = Math.max(...attachments.map((att)=>att.id)) + 1;\n            const newAttachment = {\n                ...newFile,\n                id,\n                downloadCount: 0,\n                uploadDate: new Date().toISOString().split(\"T\")[0],\n                size: newFile.size || \"1.0 MB\"\n            };\n            setAttachments([\n                ...attachments,\n                newAttachment\n            ]);\n            setNewFile({\n                name: \"\",\n                type: \"\",\n                size: \"\",\n                description: \"\",\n                category: \"\"\n            });\n            setShowUploadForm(false);\n            alert(\"تم رفع الملف بنجاح!\");\n        } else {\n            alert(\"يرجى إدخال جميع البيانات المطلوبة\");\n        }\n    };\n    const handleDelete = (id)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الملف؟\")) {\n            setAttachments(attachments.filter((att)=>att.id !== id));\n        }\n    };\n    const getFileIcon = (type)=>{\n        switch(type.toLowerCase()){\n            case \"pdf\":\n                return \"\\uD83D\\uDCC4\";\n            case \"excel\":\n                return \"\\uD83D\\uDCCA\";\n            case \"zip\":\n                return \"\\uD83D\\uDCE6\";\n            case \"mp4\":\n                return \"\\uD83C\\uDFA5\";\n            default:\n                return \"\\uD83D\\uDCC1\";\n        }\n    };\n    const getTotalSize = ()=>{\n        return attachments.reduce((total, file)=>{\n            const size = parseFloat(file.size);\n            const unit = file.size.includes(\"MB\") ? 1 : file.size.includes(\"KB\") ? 0.001 : 1;\n            return total + size * unit;\n        }, 0).toFixed(1);\n    };\n    const getTotalDownloads = ()=>{\n        return attachments.reduce((total, file)=>total + file.downloadCount, 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageTemplate, {\n        title: \"مرفقات التطبيق\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-center text-green-700 mb-6\",\n                    children: \"الملفات والمرفقات المساعدة\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg border-2 border-blue-200 flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-blue-800\",\n                                            children: file.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                file.type,\n                                                \" - \",\n                                                file.size\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"button-3d px-4 py-2 rounded bg-green-200 hover:bg-green-300 text-sm\",\n                                    children: \"تحميل\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center gap-4 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"button-3d px-8 py-3 rounded-lg bg-blue-200 hover:bg-blue-300 font-bold\",\n                            children: \"إضافة ملف\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold\",\n                            children: \"تحديث المرفقات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\attachments\\\\page.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n_s(Attachments, \"RKpmVroXk6O8+2azjQzE3faL6TE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Attachments;\nvar _c;\n$RefreshReg$(_c, \"Attachments\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/attachments/page.tsx\n"));

/***/ })

});