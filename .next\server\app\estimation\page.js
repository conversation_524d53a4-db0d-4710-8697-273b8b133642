/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/estimation/page";
exports.ids = ["app/estimation/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Festimation%2Fpage&page=%2Festimation%2Fpage&appPaths=%2Festimation%2Fpage&pagePath=private-next-app-dir%2Festimation%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Festimation%2Fpage&page=%2Festimation%2Fpage&appPaths=%2Festimation%2Fpage&pagePath=private-next-app-dir%2Festimation%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'estimation',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/estimation/page.tsx */ \"(rsc)/./src/app/estimation/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/estimation/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/estimation/page\",\n        pathname: \"/estimation\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Festimation%2Fpage&page=%2Festimation%2Fpage&appPaths=%2Festimation%2Fpage&pagePath=private-next-app-dir%2Festimation%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cestimation%5Cpage.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cestimation%5Cpage.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/estimation/page.tsx */ \"(ssr)/./src/app/estimation/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNzcmMlNUNhcHAlNUNlc3RpbWF0aW9uJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvPzg5NmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxxdXJhYW5cXFxcRGVza3RvcFxcXFxhbGFmXFxcXHNyY1xcXFxhcHBcXFxcZXN0aW1hdGlvblxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cestimation%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/estimation/page.tsx":
/*!*************************************!*\
  !*** ./src/app/estimation/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EstimationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction EstimationPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // State for animal information\n    const [animalType, setAnimalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"أبقار الحليب\");\n    const [liveWeight, setLiveWeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [milkProduction, setMilkProduction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [productionLevel, setProductionLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"منخفض\");\n    const [bodyCondition, setBodyCondition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for feeding method\n    const [feedingMethod, setFeedingMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"TMR\");\n    const [mealsPerDay, setMealsPerDay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"4-3\");\n    const [concentrateRatio, setConcentrateRatio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"60\");\n    const [roughageRatio, setRoughageRatio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"40\");\n    // Calculate feeding recommendations\n    const calculateRecommendations = ()=>{\n        const weight = parseFloat(liveWeight) || 0;\n        const milk = parseFloat(milkProduction) || 0;\n        if (weight === 0) {\n            alert(\"يرجى إدخال وزن الحيوان\");\n            return;\n        }\n        // Basic calculations for dairy cows\n        const maintenanceEnergy = weight * 0.077 // Mcal/day\n        ;\n        const milkEnergy = milk * 0.74 // Mcal/day for milk production\n        ;\n        const totalEnergy = maintenanceEnergy + milkEnergy;\n        const maintenanceProtein = weight * 0.004 // kg/day\n        ;\n        const milkProtein = milk * 0.078 // kg/day for milk production\n        ;\n        const totalProtein = maintenanceProtein + milkProtein;\n        const dryMatterIntake = weight * 0.03 + milk * 0.4 // kg/day\n        ;\n        const recommendations = {\n            totalEnergy: totalEnergy.toFixed(1),\n            totalProtein: totalProtein.toFixed(2),\n            dryMatterIntake: dryMatterIntake.toFixed(1),\n            concentrateAmount: (dryMatterIntake * (parseFloat(concentrateRatio) / 100)).toFixed(1),\n            roughageAmount: (dryMatterIntake * (parseFloat(roughageRatio) / 100)).toFixed(1)\n        };\n        alert(`التوصيات الغذائية:\nالطاقة المطلوبة: ${recommendations.totalEnergy} ميجا كالوري/يوم\nالبروتين المطلوب: ${recommendations.totalProtein} كجم/يوم\nالمادة الجافة: ${recommendations.dryMatterIntake} كجم/يوم\nالعلف المركز: ${recommendations.concentrateAmount} كجم/يوم\nالعلف الخشن: ${recommendations.roughageAmount} كجم/يوم`);\n    };\n    const saveData = ()=>{\n        const data = {\n            animalType,\n            liveWeight,\n            milkProduction,\n            productionLevel,\n            bodyCondition,\n            feedingMethod,\n            mealsPerDay,\n            concentrateRatio,\n            roughageRatio,\n            date: new Date().toLocaleDateString(\"ar-SA\")\n        };\n        localStorage.setItem(\"feedingEstimation\", JSON.stringify(data));\n        alert(\"تم حفظ البيانات بنجاح!\");\n    };\n    const loadData = ()=>{\n        const savedData = localStorage.getItem(\"feedingEstimation\");\n        if (savedData) {\n            const data = JSON.parse(savedData);\n            setAnimalType(data.animalType || \"أبقار الحليب\");\n            setLiveWeight(data.liveWeight || \"\");\n            setMilkProduction(data.milkProduction || \"\");\n            setProductionLevel(data.productionLevel || \"منخفض\");\n            setBodyCondition(data.bodyCondition || \"\");\n            setFeedingMethod(data.feedingMethod || \"TMR\");\n            setMealsPerDay(data.mealsPerDay || \"4-3\");\n            setConcentrateRatio(data.concentrateRatio || \"60\");\n            setRoughageRatio(data.roughageRatio || \"40\");\n            alert(\"تم تحميل البيانات المحفوظة!\");\n        } else {\n            alert(\"لا توجد بيانات محفوظة\");\n        }\n    };\n    const resetForm = ()=>{\n        setAnimalType(\"أبقار الحليب\");\n        setLiveWeight(\"\");\n        setMilkProduction(\"\");\n        setProductionLevel(\"منخفض\");\n        setBodyCondition(\"\");\n        setFeedingMethod(\"TMR\");\n        setMealsPerDay(\"4-3\");\n        setConcentrateRatio(\"60\");\n        setRoughageRatio(\"40\");\n        alert(\"تم إعادة تعيين النموذج!\");\n    };\n    const printReport = ()=>{\n        const printContent = `\n      <html>\n        <head>\n          <title>تقرير تقدير طريقة التغذية</title>\n          <style>\n            body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }\n            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 10px; }\n            .section { margin: 20px 0; }\n            .section h3 { background-color: #f0f0f0; padding: 10px; margin: 0; }\n            .content { padding: 15px; border: 1px solid #ddd; }\n            .row { display: flex; justify-content: space-between; margin: 5px 0; }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <h1>تقرير تقدير طريقة التغذية</h1>\n            <p>التاريخ: ${new Date().toLocaleDateString(\"ar-SA\")}</p>\n          </div>\n\n          <div class=\"section\">\n            <h3>معلومات الحيوان</h3>\n            <div class=\"content\">\n              <div class=\"row\"><span>نوع الحيوان:</span><span>${animalType}</span></div>\n              <div class=\"row\"><span>الوزن الحي:</span><span>${liveWeight} كجم</span></div>\n              <div class=\"row\"><span>إنتاج الحليب:</span><span>${milkProduction} لتر</span></div>\n              <div class=\"row\"><span>مستوى الإنتاج:</span><span>${productionLevel}</span></div>\n              <div class=\"row\"><span>حالة الجسم:</span><span>${bodyCondition}</span></div>\n            </div>\n          </div>\n\n          <div class=\"section\">\n            <h3>طريقة التغذية المقترحة</h3>\n            <div class=\"content\">\n              <div class=\"row\"><span>نظام التغذية:</span><span>${feedingMethod}</span></div>\n              <div class=\"row\"><span>عدد الوجبات:</span><span>${mealsPerDay} وجبات يومياً</span></div>\n              <div class=\"row\"><span>نسبة العلف المركز:</span><span>${concentrateRatio}%</span></div>\n              <div class=\"row\"><span>نسبة العلف الخشن:</span><span>${roughageRatio}%</span></div>\n            </div>\n          </div>\n        </body>\n      </html>\n    `;\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            printWindow.document.write(printContent);\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-300 via-blue-100 to-blue-300 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"px-6 py-3 bg-gray-200 rounded-lg border-2 border-gray-400 button-3d hover:bg-gray-300 transition-all\",\n                            children: \"\\uD83C\\uDFE0 الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-blue-900\",\n                            children: \"تقدير طريقة التغذية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.back(),\n                            className: \"px-6 py-3 bg-gray-200 rounded-lg border-2 border-gray-400 button-3d hover:bg-gray-300 transition-all\",\n                            children: \"← العودة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wood-texture rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-center text-green-700 mb-6\",\n                                children: \"نظام تقدير طرق التغذية المثلى\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-blue-700 mb-4 text-center\",\n                                                children: \"معلومات الحيوان\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block font-bold mb-2\",\n                                                                children: \"نوع الحيوان\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: animalType,\n                                                                onChange: (e)=>setAnimalType(e.target.value),\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"أبقار الحليب\",\n                                                                        children: \"أبقار الحليب\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 207,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"أبقار اللحم\",\n                                                                        children: \"أبقار اللحم\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 208,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الجاموس\",\n                                                                        children: \"الجاموس\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الأغنام\",\n                                                                        children: \"الأغنام\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"الماعز\",\n                                                                        children: \"الماعز\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block font-bold mb-2\",\n                                                                children: \"الوزن الحي (كجم)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: liveWeight,\n                                                                onChange: (e)=>setLiveWeight(e.target.value),\n                                                                placeholder: \"أدخل الوزن\",\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block font-bold mb-2\",\n                                                                children: \"إنتاج الحليب (لتر/يوم)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: milkProduction,\n                                                                onChange: (e)=>setMilkProduction(e.target.value),\n                                                                placeholder: \"أدخل إنتاج الحليب\",\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block font-bold mb-2\",\n                                                                children: \"مستوى الإنتاج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: productionLevel,\n                                                                onChange: (e)=>setProductionLevel(e.target.value),\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"منخفض\",\n                                                                        children: \"منخفض\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"متوسط\",\n                                                                        children: \"متوسط\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"عالي\",\n                                                                        children: \"عالي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ممتاز\",\n                                                                        children: \"ممتاز\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block font-bold mb-2\",\n                                                                children: \"حالة الجسم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: bodyCondition,\n                                                                onChange: (e)=>setBodyCondition(e.target.value),\n                                                                placeholder: \"وصف حالة الجسم\",\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-green-700 mb-4 text-center\",\n                                                children: \"التوصيات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg border-2 border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block font-bold mb-2\",\n                                                                    children: \"طريقة التغذية المقترحة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: feedingMethod,\n                                                                    onChange: (e)=>setFeedingMethod(e.target.value),\n                                                                    className: \"w-full p-2 border rounded\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"TMR\",\n                                                                            children: \"نظام التغذية: تغذية مختلطة (TMR)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"منفصلة\",\n                                                                            children: \"تغذية منفصلة\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"مرحلية\",\n                                                                            children: \"تغذية مرحلية\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block font-bold mb-2\",\n                                                                    children: \"عدد الوجبات:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: mealsPerDay,\n                                                                    onChange: (e)=>setMealsPerDay(e.target.value),\n                                                                    className: \"w-full p-2 border rounded\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"2\",\n                                                                            children: \"2 وجبات يومياً\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"4-3\",\n                                                                            children: \"3-4 وجبات يومياً\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"6-5\",\n                                                                            children: \"5-6 وجبات يومياً\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block font-bold mb-2\",\n                                                                    children: [\n                                                                        \"نسبة العلف المركز: \",\n                                                                        concentrateRatio,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"range\",\n                                                                    min: \"30\",\n                                                                    max: \"80\",\n                                                                    value: concentrateRatio,\n                                                                    onChange: (e)=>{\n                                                                        setConcentrateRatio(e.target.value);\n                                                                        setRoughageRatio((100 - parseInt(e.target.value)).toString());\n                                                                    },\n                                                                    className: \"w-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block font-bold mb-2\",\n                                                                    children: [\n                                                                        \"نسبة العلف الخشن: \",\n                                                                        roughageRatio,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"range\",\n                                                                    min: \"20\",\n                                                                    max: \"70\",\n                                                                    value: roughageRatio,\n                                                                    onChange: (e)=>{\n                                                                        setRoughageRatio(e.target.value);\n                                                                        setConcentrateRatio((100 - parseInt(e.target.value)).toString());\n                                                                    },\n                                                                    className: \"w-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 flex flex-wrap justify-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: calculateRecommendations,\n                                        className: \"px-6 py-3 bg-blue-500 text-white rounded-lg button-3d hover:bg-blue-600 transition-all\",\n                                        children: \"حساب التوصيات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveData,\n                                        className: \"px-6 py-3 bg-green-500 text-white rounded-lg button-3d hover:bg-green-600 transition-all\",\n                                        children: \"حفظ البيانات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: loadData,\n                                        className: \"px-6 py-3 bg-yellow-500 text-white rounded-lg button-3d hover:bg-yellow-600 transition-all\",\n                                        children: \"تحميل البيانات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: printReport,\n                                        className: \"px-6 py-3 bg-purple-500 text-white rounded-lg button-3d hover:bg-purple-600 transition-all\",\n                                        children: \"طباعة التقرير\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: resetForm,\n                                        className: \"px-6 py-3 bg-red-500 text-white rounded-lg button-3d hover:bg-red-600 transition-all\",\n                                        children: \"إعادة تعيين\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/feed-calculator\"),\n                                        className: \"px-6 py-3 bg-orange-500 text-white rounded-lg button-3d hover:bg-orange-600 transition-all\",\n                                        children: \"انتقال لحاسبة العلائق\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\estimation\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/estimation/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"09bb1e9faddc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzIxZmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOWJiMWU5ZmFkZGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/estimation/page.tsx":
/*!*************************************!*\
  !*** ./src/app/estimation/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\alaf\src\app\estimation\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"برنامج التغذية التطبيقية للأبقار\",\n    description: \"برنامج تكوين الأعلاف للأبقار\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO2tCQUNsQiw0RUFBQ0M7c0JBQU1KOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfYqNix2YbYp9mF2Kwg2KfZhNiq2LrYsNmK2Kkg2KfZhNiq2LfYqNmK2YLZitipINmE2YTYo9io2YLYp9ixJyxcbiAgZGVzY3JpcHRpb246ICfYqNix2YbYp9mF2Kwg2KrZg9mI2YrZhiDYp9mE2KPYudmE2KfZgSDZhNmE2KPYqNmC2KfYsScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImRpciIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Festimation%2Fpage&page=%2Festimation%2Fpage&appPaths=%2Festimation%2Fpage&pagePath=private-next-app-dir%2Festimation%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();