/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/ration-division/page";
exports.ids = ["app/ration-division/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fration-division%2Fpage&page=%2Fration-division%2Fpage&appPaths=%2Fration-division%2Fpage&pagePath=private-next-app-dir%2Fration-division%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fration-division%2Fpage&page=%2Fration-division%2Fpage&appPaths=%2Fration-division%2Fpage&pagePath=private-next-app-dir%2Fration-division%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'ration-division',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ration-division/page.tsx */ \"(rsc)/./src/app/ration-division/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/ration-division/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/ration-division/page\",\n        pathname: \"/ration-division\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fration-division%2Fpage&page=%2Fration-division%2Fpage&appPaths=%2Fration-division%2Fpage&pagePath=private-next-app-dir%2Fration-division%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cration-division%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cration-division%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ration-division/page.tsx */ \"(ssr)/./src/app/ration-division/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcXVyYWFuJTVDRGVza3RvcCU1Q2FsYWYlNUNzcmMlNUNhcHAlNUNyYXRpb24tZGl2aXNpb24lNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mZWVkLWZvcm11bGF0aW9uLWFwcC8/MzViYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHF1cmFhblxcXFxEZXNrdG9wXFxcXGFsYWZcXFxcc3JjXFxcXGFwcFxcXFxyYXRpb24tZGl2aXNpb25cXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cration-division%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/ration-division/page.tsx":
/*!******************************************!*\
  !*** ./src/app/ration-division/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RationDivision)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PageTemplate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PageTemplate */ \"(ssr)/./src/components/PageTemplate.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction RationDivision() {\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animalCounts, setAnimalCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        group1: 0,\n        group2: 0,\n        group3: 0\n    });\n    const [showCalculation, setShowCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const groups = [\n        {\n            id: 1,\n            name: \"المجموعة الأولى\",\n            production: \"أبقار عالية الإنتاج (أكثر من 30 لتر/يوم)\",\n            count: animalCounts.group1,\n            concentrate: \"12-15 كجم\",\n            roughage: \"8-10 كجم\",\n            protein: \"18-20%\",\n            energy: \"1.7-1.9 ميجا كالوري\"\n        },\n        {\n            id: 2,\n            name: \"المجموعة الثانية\",\n            production: \"أبقار متوسطة الإنتاج (20-30 لتر/يوم)\",\n            count: animalCounts.group2,\n            concentrate: \"8-12 كجم\",\n            roughage: \"10-12 كجم\",\n            protein: \"16-18%\",\n            energy: \"1.5-1.7 ميجا كالوري\"\n        },\n        {\n            id: 3,\n            name: \"المجموعة الثالثة\",\n            production: \"أبقار منخفضة الإنتاج (أقل من 20 لتر/يوم)\",\n            count: animalCounts.group3,\n            concentrate: \"5-8 كجم\",\n            roughage: \"12-15 كجم\",\n            protein: \"14-16%\",\n            energy: \"1.3-1.5 ميجا كالوري\"\n        }\n    ];\n    const handleGroupClassification = ()=>{\n        setSelectedGroup(null);\n        setShowCalculation(false);\n        // Show classification form\n        alert(\"سيتم فتح نموذج تصنيف القطيع\");\n    };\n    const handleQuantityCalculation = ()=>{\n        if (animalCounts.group1 === 0 && animalCounts.group2 === 0 && animalCounts.group3 === 0) {\n            alert(\"يرجى إدخال عدد الحيوانات في كل مجموعة أولاً\");\n            return;\n        }\n        setShowCalculation(true);\n    };\n    const updateAnimalCount = (group, count)=>{\n        setAnimalCounts((prev)=>({\n                ...prev,\n                [group]: Math.max(0, count)\n            }));\n    };\n    const calculateTotalFeed = (group)=>{\n        const concentrateMin = parseInt(group.concentrate.split(\"-\")[0]);\n        const concentrateMax = parseInt(group.concentrate.split(\"-\")[1]);\n        const roughageMin = parseInt(group.roughage.split(\"-\")[0]);\n        const roughageMax = parseInt(group.roughage.split(\"-\")[1]);\n        const concentrateAvg = (concentrateMin + concentrateMax) / 2;\n        const roughageAvg = (roughageMin + roughageMax) / 2;\n        return {\n            concentrate: (concentrateAvg * group.count).toFixed(1),\n            roughage: (roughageAvg * group.count).toFixed(1),\n            total: ((concentrateAvg + roughageAvg) * group.count).toFixed(1)\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PageTemplate__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: \"قسمة العلائق\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-center text-green-700 mb-6\",\n                    children: \"نظام تقسيم العلائق حسب المجموعات\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: groups.map((group, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `p-6 rounded-lg border-2 cursor-pointer transition-all duration-300 ${index === 0 ? \"bg-blue-50 border-blue-200 hover:bg-blue-100\" : index === 1 ? \"bg-green-50 border-green-200 hover:bg-green-100\" : \"bg-yellow-50 border-yellow-200 hover:bg-yellow-100\"} ${selectedGroup === group.id ? \"ring-4 ring-blue-300\" : \"\"}`,\n                            onClick: ()=>setSelectedGroup(selectedGroup === group.id ? null : group.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: `text-xl font-bold mb-4 ${index === 0 ? \"text-blue-700\" : index === 1 ? \"text-green-700\" : \"text-yellow-700\"}`,\n                                    children: group.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mb-3\",\n                                    children: group.production\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"عدد الحيوانات:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            value: group.count,\n                                            onChange: (e)=>updateAnimalCount(`group${group.id}`, parseInt(e.target.value) || 0),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• علف مركز: \",\n                                                group.concentrate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• علف خشن: \",\n                                                group.roughage\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• بروتين: \",\n                                                group.protein\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• طاقة: \",\n                                                group.energy\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                group.count > 0 && showCalculation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-white rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-sm mb-2\",\n                                            children: \"الكميات المطلوبة يومياً:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"علف مركز: \",\n                                                        calculateTotalFeed(group).concentrate,\n                                                        \" كجم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"علف خشن: \",\n                                                        calculateTotalFeed(group).roughage,\n                                                        \" كجم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-bold\",\n                                                    children: [\n                                                        \"المجموع: \",\n                                                        calculateTotalFeed(group).total,\n                                                        \" كجم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center gap-4 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGroupClassification,\n                            className: \"button-3d px-8 py-3 rounded-lg bg-blue-200 hover:bg-blue-300 font-bold transition-colors duration-300\",\n                            children: \"تصنيف القطيع\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleQuantityCalculation,\n                            className: \"button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold transition-colors duration-300\",\n                            children: \"حساب الكميات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                showCalculation && (animalCounts.group1 > 0 || animalCounts.group2 > 0 || animalCounts.group3 > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 p-6 bg-gray-50 rounded-lg border-2 border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-center text-gray-700 mb-4\",\n                            children: \"ملخص الكميات المطلوبة يومياً\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-4 rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-blue-600 mb-2\",\n                                            children: \"إجمالي العلف المركز:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-blue-700\",\n                                            children: [\n                                                groups.reduce((total, group)=>total + parseFloat(calculateTotalFeed(group).concentrate), 0).toFixed(1),\n                                                \" كجم\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-4 rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-green-600 mb-2\",\n                                            children: \"إجمالي العلف الخشن:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-green-700\",\n                                            children: [\n                                                groups.reduce((total, group)=>total + parseFloat(calculateTotalFeed(group).roughage), 0).toFixed(1),\n                                                \" كجم\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-bold text-gray-600 mb-2\",\n                                        children: \"إجمالي العلف المطلوب:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-gray-700\",\n                                        children: [\n                                            groups.reduce((total, group)=>total + parseFloat(calculateTotalFeed(group).total), 0).toFixed(1),\n                                            \" كجم/يوم\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ration-division/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PageTemplate.tsx":
/*!*****************************************!*\
  !*** ./src/components/PageTemplate.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageTemplate)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction PageTemplate({ title, children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-300 via-blue-100 to-blue-300 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.back(),\n                            className: \"button-3d px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300\",\n                            children: \"← العودة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\components\\\\PageTemplate.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-blue-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\components\\\\PageTemplate.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"button-3d px-6 py-2 rounded-lg bg-green-200 hover:bg-green-300\",\n                            children: \"\\uD83C\\uDFE0 الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\components\\\\PageTemplate.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\components\\\\PageTemplate.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wood-texture rounded-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 shadow-lg\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\components\\\\PageTemplate.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\components\\\\PageTemplate.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\components\\\\PageTemplate.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\components\\\\PageTemplate.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PageTemplate.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"09bb1e9faddc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzIxZmIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOWJiMWU5ZmFkZGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"برنامج التغذية التطبيقية للأبقار\",\n    description: \"برنامج تكوين الأعلاف للأبقار\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO2tCQUNsQiw0RUFBQ0M7c0JBQU1KOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmVlZC1mb3JtdWxhdGlvbi1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfYqNix2YbYp9mF2Kwg2KfZhNiq2LrYsNmK2Kkg2KfZhNiq2LfYqNmK2YLZitipINmE2YTYo9io2YLYp9ixJyxcbiAgZGVzY3JpcHRpb246ICfYqNix2YbYp9mF2Kwg2KrZg9mI2YrZhiDYp9mE2KPYudmE2KfZgSDZhNmE2KPYqNmC2KfYsScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImRpciIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/ration-division/page.tsx":
/*!******************************************!*\
  !*** ./src/app/ration-division/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\alaf\src\app\ration-division\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fration-division%2Fpage&page=%2Fration-division%2Fpage&appPaths=%2Fration-division%2Fpage&pagePath=private-next-app-dir%2Fration-division%2Fpage.tsx&appDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();