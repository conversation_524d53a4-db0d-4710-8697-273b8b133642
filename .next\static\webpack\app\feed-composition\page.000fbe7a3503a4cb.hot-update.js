"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed-composition/page",{

/***/ "(app-pages-browser)/./src/app/feed-composition/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/feed-composition/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FeedComposition; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction FeedComposition() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [weight, setWeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [milkProduction, setMilkProduction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [productionStage, setProductionStage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [requirements, setRequirements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedComposition, setFeedComposition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const calculateRequirements = ()=>{\n        if (!weight || !milkProduction || !productionStage) {\n            alert(\"يرجى إدخال جميع البيانات المطلوبة\");\n            return;\n        }\n        // حسابات الاحتياجات الغذائية بناءً على الوزن وإنتاج الحليب\n        const baseMaintenanceEnergy = weight * 0.077 // ميجا كالوري للصيانة\n        ;\n        const milkEnergy = milkProduction * 0.75 // ميجا كالوري لإنتاج الحليب\n        ;\n        const totalEnergy = baseMaintenanceEnergy + milkEnergy;\n        // تعديل حسب مرحلة الإنتاج\n        let stageMultiplier = 1.0;\n        switch(productionStage){\n            case \"بداية الإدرار\":\n                stageMultiplier = 1.2;\n                break;\n            case \"ذروة الإنتاج\":\n                stageMultiplier = 1.3;\n                break;\n            case \"منتصف الإدرار\":\n                stageMultiplier = 1.1;\n                break;\n            case \"نهاية الإدرار\":\n                stageMultiplier = 0.9;\n                break;\n            case \"فترة الجفاف\":\n                stageMultiplier = 0.8;\n                break;\n        }\n        const adjustedEnergy = totalEnergy * stageMultiplier;\n        const dryMatter = weight * 0.03 + milkProduction * 0.15 // كجم مادة جافة\n        ;\n        const crudeProtein = dryMatter * 0.16 // 16% بروتين من المادة الجافة\n        ;\n        const calcium = milkProduction * 2.5 + weight * 0.04 // جرام كالسيوم\n        ;\n        const phosphorus = calcium * 0.8 // نسبة الفوسفور للكالسيوم\n        ;\n        const newRequirements = {\n            dryMatter: Math.round(dryMatter * 10) / 10,\n            crudeProtein: Math.round(crudeProtein * 100) / 100,\n            digestibleEnergy: Math.round(adjustedEnergy * 10) / 10,\n            calcium: Math.round(calcium),\n            phosphorus: Math.round(phosphorus)\n        };\n        setRequirements(newRequirements);\n        setShowResults(true);\n    };\n    const composeFeed = ()=>{\n        if (!requirements) {\n            alert(\"يرجى حساب الاحتياجات أولاً\");\n            return;\n        }\n        // حساب تركيب العليقة\n        const concentrateRatio = milkProduction > 20 ? 0.6 : milkProduction > 10 ? 0.5 : 0.4;\n        const roughageRatio = 1 - concentrateRatio;\n        const concentrate = requirements.dryMatter * concentrateRatio;\n        const roughage = requirements.dryMatter * roughageRatio;\n        const composition = {\n            concentrate: Math.round(concentrate * 10) / 10,\n            roughage: Math.round(roughage * 10) / 10,\n            protein: Math.round(requirements.crudeProtein / requirements.dryMatter * 100 * 10) / 10,\n            energy: Math.round(requirements.digestibleEnergy / requirements.dryMatter * 10) / 10,\n            calcium: requirements.calcium,\n            phosphorus: requirements.phosphorus\n        };\n        setFeedComposition(composition);\n    };\n    const printResults = ()=>{\n        if (!requirements) {\n            alert(\"لا توجد نتائج للطباعة\");\n            return;\n        }\n        const printContent = \"\\n      تقرير الاحتياجات الغذائية\\n      ========================\\n\\n      معلومات الحيوان:\\n      - الوزن: \".concat(weight, \" كجم\\n      - إنتاج الحليب: \").concat(milkProduction, \" لتر/يوم\\n      - مرحلة الإنتاج: \").concat(productionStage, \"\\n\\n      الاحتياجات الغذائية:\\n      - المادة الجافة: \").concat(requirements.dryMatter, \" كجم/يوم\\n      - البروتين الخام: \").concat(requirements.crudeProtein, \" كجم/يوم\\n      - الطاقة المهضومة: \").concat(requirements.digestibleEnergy, \" ميجا كالوري/يوم\\n      - الكالسيوم: \").concat(requirements.calcium, \" جرام/يوم\\n      - الفوسفور: \").concat(requirements.phosphorus, \" جرام/يوم\\n\\n      \").concat(feedComposition ? \"\\n      تركيب العليقة:\\n      - علف مركز: \".concat(feedComposition.concentrate, \" كجم/يوم\\n      - علف خشن: \").concat(feedComposition.roughage, \" كجم/يوم\\n      - نسبة البروتين: \").concat(feedComposition.protein, \"%\\n      - الطاقة لكل كجم: \").concat(feedComposition.energy, \" ميجا كالوري\\n      \") : \"\", \"\\n    \");\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>تقرير الاحتياجات الغذائية</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }\\n              h1 { color: #2563eb; text-align: center; }\\n              pre { white-space: pre-wrap; font-size: 14px; line-height: 1.6; }\\n            </style>\\n          </head>\\n          <body>\\n            <h1>برنامج تركيب الخلائط العلفية</h1>\\n            <pre>\".concat(printContent, \"</pre>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-300 via-blue-100 to-blue-300 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.back(),\n                            className: \"button-3d px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300\",\n                            children: \"← العودة\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-blue-900\",\n                            children: \"تركيب خلائط علفية للأبقار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"wood-texture rounded-lg p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-center mb-6 text-green-700\",\n                                children: \"نظام تركيب الخلائط العلفية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-blue-700 mb-4\",\n                                                children: \"معلومات الحيوان\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-bold mb-2\",\n                                                                children: \"وزن الحيوان (كجم)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: weight || \"\",\n                                                                onChange: (e)=>setWeight(Number(e.target.value)),\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\",\n                                                                placeholder: \"500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-bold mb-2\",\n                                                                children: \"إنتاج الحليب اليومي (لتر)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: milkProduction || \"\",\n                                                                onChange: (e)=>setMilkProduction(Number(e.target.value)),\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\",\n                                                                placeholder: \"40\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-bold mb-2\",\n                                                                children: \"مرحلة الإنتاج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: productionStage,\n                                                                onChange: (e)=>setProductionStage(e.target.value),\n                                                                className: \"w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"اختر المرحلة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"بداية الإدرار\",\n                                                                        children: \"بداية الإدرار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ذروة الإنتاج\",\n                                                                        children: \"ذروة الإنتاج\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"منتصف الإدرار\",\n                                                                        children: \"منتصف الإدرار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"نهاية الإدرار\",\n                                                                        children: \"نهاية الإدرار\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"فترة الجفاف\",\n                                                                        children: \"فترة الجفاف\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-green-700 mb-4\",\n                                                children: \"الاحتياجات الغذائية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg border-2 border-green-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"المادة الجافة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? \"\".concat(requirements.dryMatter, \" كجم/يوم\") : \"-- كجم/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"البروتين الخام:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? \"\".concat(requirements.crudeProtein, \" كجم/يوم\") : \"-- كجم/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"الطاقة المهضومة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? \"\".concat(requirements.digestibleEnergy, \" ميجا كالوري/يوم\") : \"-- ميجا كالوري/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"الكالسيوم:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? \"\".concat(requirements.calcium, \" جرام/يوم\") : \"-- جرام/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"الفوسفور:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: requirements ? \"\".concat(requirements.phosphorus, \" جرام/يوم\") : \"-- جرام/يوم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            feedComposition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-blue-50 rounded-lg border-2 border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-blue-700 mb-4 text-center\",\n                                        children: \"تركيب العليقة المقترحة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white p-3 rounded border\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-green-600 mb-2\",\n                                                        children: \"المكونات الأساسية:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"علف مركز: \",\n                                                                    feedComposition.concentrate,\n                                                                    \" كجم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"علف خشن: \",\n                                                                    feedComposition.roughage,\n                                                                    \" كجم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-bold\",\n                                                                children: [\n                                                                    \"المجموع: \",\n                                                                    (feedComposition.concentrate + feedComposition.roughage).toFixed(1),\n                                                                    \" كجم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white p-3 rounded border\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-orange-600 mb-2\",\n                                                        children: \"التركيب الغذائي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"نسبة البروتين: \",\n                                                                    feedComposition.protein,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"الطاقة/كجم: \",\n                                                                    feedComposition.energy,\n                                                                    \" ميجا كالوري\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"الكالسيوم: \",\n                                                                    feedComposition.calcium,\n                                                                    \" جم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"الفوسفور: \",\n                                                                    feedComposition.phosphorus,\n                                                                    \" جم/يوم\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center gap-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: calculateRequirements,\n                                        className: \"button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold transition-colors duration-300\",\n                                        children: \"حساب الاحتياجات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: composeFeed,\n                                        disabled: !requirements,\n                                        className: \"button-3d px-8 py-3 rounded-lg font-bold transition-colors duration-300 \".concat(requirements ? \"bg-blue-200 hover:bg-blue-300\" : \"bg-gray-200 cursor-not-allowed opacity-50\"),\n                                        children: \"تركيب العليقة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: printResults,\n                                        disabled: !requirements,\n                                        className: \"button-3d px-8 py-3 rounded-lg font-bold transition-colors duration-300 \".concat(requirements ? \"bg-yellow-200 hover:bg-yellow-300\" : \"bg-gray-200 cursor-not-allowed opacity-50\"),\n                                        children: \"طباعة النتائج\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\feed-composition\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedComposition, \"Bh0oaMvUYyOKU35yCP95Y9mXWsY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = FeedComposition;\nvar _c;\n$RefreshReg$(_c, \"FeedComposition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed-composition/page.tsx\n"));

/***/ })

});