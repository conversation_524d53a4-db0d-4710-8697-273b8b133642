[{"C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\animal-type\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\attachments\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\content-analysis\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\cow-info\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\estimation\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\feed-calculator\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\feed-composition\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\feed-evaluation\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\open-file\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\print\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\ration-division\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\references\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\safety-studies\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\technical-conditions\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\view-table\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\alaf\\src\\components\\PageTemplate.tsx": "18"}, {"size": 10351, "mtime": 1749027123793, "results": "19", "hashOfConfig": "20"}, {"size": 16680, "mtime": 1749037594359, "results": "21", "hashOfConfig": "20"}, {"size": 4800, "mtime": 1749027841996, "results": "22", "hashOfConfig": "20"}, {"size": 7235, "mtime": 1749029611060, "results": "23", "hashOfConfig": "20"}, {"size": 15775, "mtime": 1749032047172, "results": "24", "hashOfConfig": "20"}, {"size": 29235, "mtime": 1749035644604, "results": "25", "hashOfConfig": "20"}, {"size": 13854, "mtime": 1749036365708, "results": "26", "hashOfConfig": "20"}, {"size": 7675, "mtime": 1749027073216, "results": "27", "hashOfConfig": "20"}, {"size": 423, "mtime": 1749026137487, "results": "28", "hashOfConfig": "20"}, {"size": 2413, "mtime": 1749027822267, "results": "29", "hashOfConfig": "20"}, {"size": 7595, "mtime": 1749029638994, "results": "30", "hashOfConfig": "20"}, {"size": 4027, "mtime": 1749028056666, "results": "31", "hashOfConfig": "20"}, {"size": 8073, "mtime": 1749035892285, "results": "32", "hashOfConfig": "20"}, {"size": 2947, "mtime": 1749027354254, "results": "33", "hashOfConfig": "20"}, {"size": 2174, "mtime": 1749027669914, "results": "34", "hashOfConfig": "20"}, {"size": 2898, "mtime": 1749027797864, "results": "35", "hashOfConfig": "20"}, {"size": 20765, "mtime": 1749036848141, "results": "36", "hashOfConfig": "20"}, {"size": 1252, "mtime": 1749027194350, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "swpy56", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\animal-type\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\attachments\\page.tsx", ["92", "93"], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\content-analysis\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\cow-info\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\estimation\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\feed-calculator\\page.tsx", ["94"], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\feed-composition\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\feed-evaluation\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\open-file\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\print\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\ration-division\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\references\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\safety-studies\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\technical-conditions\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\app\\view-table\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\alaf\\src\\components\\PageTemplate.tsx", [], [], {"ruleId": "95", "severity": 2, "message": "96", "line": 401, "column": 28, "nodeType": "97", "messageId": "98", "suggestions": "99"}, {"ruleId": "95", "severity": 2, "message": "96", "line": 401, "column": 34, "nodeType": "97", "messageId": "98", "suggestions": "100"}, {"ruleId": "101", "severity": 1, "message": "102", "line": 94, "column": 6, "nodeType": "103", "endLine": 94, "endColumn": 8, "suggestions": "104"}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["105", "106", "107", "108"], ["109", "110", "111", "112"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'feedData'. Either include it or remove the dependency array.", "ArrayExpression", ["113"], {"messageId": "114", "data": "115", "fix": "116", "desc": "117"}, {"messageId": "114", "data": "118", "fix": "119", "desc": "120"}, {"messageId": "114", "data": "121", "fix": "122", "desc": "123"}, {"messageId": "114", "data": "124", "fix": "125", "desc": "126"}, {"messageId": "114", "data": "127", "fix": "128", "desc": "117"}, {"messageId": "114", "data": "129", "fix": "130", "desc": "120"}, {"messageId": "114", "data": "131", "fix": "132", "desc": "123"}, {"messageId": "114", "data": "133", "fix": "134", "desc": "126"}, {"desc": "135", "fix": "136"}, "replaceWithAlt", {"alt": "137"}, {"range": "138", "text": "139"}, "Replace with `&quot;`.", {"alt": "140"}, {"range": "141", "text": "142"}, "Replace with `&ldquo;`.", {"alt": "143"}, {"range": "144", "text": "145"}, "Replace with `&#34;`.", {"alt": "146"}, {"range": "147", "text": "148"}, "Replace with `&rdquo;`.", {"alt": "137"}, {"range": "149", "text": "150"}, {"alt": "140"}, {"range": "151", "text": "152"}, {"alt": "143"}, {"range": "153", "text": "154"}, {"alt": "146"}, {"range": "155", "text": "156"}, "Update the dependencies array to be: [feedData]", {"range": "157", "text": "158"}, "&quot;", [15457, 15507], "• اضغط &quot;تحميل\" لتنزيل الملف (سيزيد عداد التحميلات)", "&ldquo;", [15457, 15507], "• اضغط &ldquo;تحميل\" لتنزيل الملف (سيزيد عداد التحميلات)", "&#34;", [15457, 15507], "• اضغط &#34;تحميل\" لتنزيل الملف (سيزيد عداد التحميلات)", "&rdquo;", [15457, 15507], "• اضغط &rdquo;تحميل\" لتنزيل الملف (سيزيد عداد التحميلات)", [15457, 15507], "• اضغط \"تحميل&quot; لتنزيل الملف (سيزيد عداد التحميلات)", [15457, 15507], "• اضغط \"تحميل&ldquo; لتنزيل الملف (سيزيد عداد التحميلات)", [15457, 15507], "• اضغط \"تحميل&#34; لتن<PERSON>يل الملف (سيزيد عداد التحميلات)", [15457, 15507], "• اضغط \"تحميل&rdquo; لتنزيل الملف (سيزيد عداد التحميلات)", [2998, 3000], "[feedData]"]