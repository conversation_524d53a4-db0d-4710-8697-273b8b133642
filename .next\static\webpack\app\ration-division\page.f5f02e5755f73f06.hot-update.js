/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ration-division/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cration-division%5Cpage.tsx&server=false!":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cration-division%5Cpage.tsx&server=false! ***!
  \***********************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ration-division/page.tsx */ \"(app-pages-browser)/./src/app/ration-division/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNxdXJhYW4lNUNEZXNrdG9wJTVDYWxhZiU1Q3NyYyU1Q2FwcCU1Q3JhdGlvbi1kaXZpc2lvbiU1Q3BhZ2UudHN4JnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9jNWFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccXVyYWFuXFxcXERlc2t0b3BcXFxcYWxhZlxcXFxzcmNcXFxcYXBwXFxcXHJhdGlvbi1kaXZpc2lvblxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cquraan%5CDesktop%5Calaf%5Csrc%5Capp%5Cration-division%5Cpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/ration-division/page.tsx":
/*!******************************************!*\
  !*** ./src/app/ration-division/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RationDivision; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PageTemplate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/PageTemplate */ \"(app-pages-browser)/./src/components/PageTemplate.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction RationDivision() {\n    _s();\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animalCounts, setAnimalCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        group1: 0,\n        group2: 0,\n        group3: 0\n    });\n    const [showCalculation, setShowCalculation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const groups = [\n        {\n            id: 1,\n            name: \"المجموعة الأولى\",\n            production: \"أبقار عالية الإنتاج (أكثر من 30 لتر/يوم)\",\n            count: animalCounts.group1,\n            concentrate: \"12-15 كجم\",\n            roughage: \"8-10 كجم\",\n            protein: \"18-20%\",\n            energy: \"1.7-1.9 ميجا كالوري\"\n        },\n        {\n            id: 2,\n            name: \"المجموعة الثانية\",\n            production: \"أبقار متوسطة الإنتاج (20-30 لتر/يوم)\",\n            count: animalCounts.group2,\n            concentrate: \"8-12 كجم\",\n            roughage: \"10-12 كجم\",\n            protein: \"16-18%\",\n            energy: \"1.5-1.7 ميجا كالوري\"\n        },\n        {\n            id: 3,\n            name: \"المجموعة الثالثة\",\n            production: \"أبقار منخفضة الإنتاج (أقل من 20 لتر/يوم)\",\n            count: animalCounts.group3,\n            concentrate: \"5-8 كجم\",\n            roughage: \"12-15 كجم\",\n            protein: \"14-16%\",\n            energy: \"1.3-1.5 ميجا كالوري\"\n        }\n    ];\n    const handleGroupClassification = ()=>{\n        setSelectedGroup(null);\n        setShowCalculation(false);\n        // Show classification form\n        alert(\"سيتم فتح نموذج تصنيف القطيع\");\n    };\n    const handleQuantityCalculation = ()=>{\n        if (animalCounts.group1 === 0 && animalCounts.group2 === 0 && animalCounts.group3 === 0) {\n            alert(\"يرجى إدخال عدد الحيوانات في كل مجموعة أولاً\");\n            return;\n        }\n        setShowCalculation(true);\n    };\n    const updateAnimalCount = (group, count)=>{\n        setAnimalCounts((prev)=>({\n                ...prev,\n                [group]: Math.max(0, count)\n            }));\n    };\n    const calculateTotalFeed = (group)=>{\n        const concentrateMin = parseInt(group.concentrate.split(\"-\")[0]);\n        const concentrateMax = parseInt(group.concentrate.split(\"-\")[1]);\n        const roughageMin = parseInt(group.roughage.split(\"-\")[0]);\n        const roughageMax = parseInt(group.roughage.split(\"-\")[1]);\n        const concentrateAvg = (concentrateMin + concentrateMax) / 2;\n        const roughageAvg = (roughageMin + roughageMax) / 2;\n        return {\n            concentrate: (concentrateAvg * group.count).toFixed(1),\n            roughage: (roughageAvg * group.count).toFixed(1),\n            total: ((concentrateAvg + roughageAvg) * group.count).toFixed(1)\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PageTemplate__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: \"قسمة العلائق\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-center text-green-700 mb-6\",\n                    children: \"نظام تقسيم العلائق حسب المجموعات\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: groups.map((group, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 rounded-lg border-2 cursor-pointer transition-all duration-300 \".concat(index === 0 ? \"bg-blue-50 border-blue-200 hover:bg-blue-100\" : index === 1 ? \"bg-green-50 border-green-200 hover:bg-green-100\" : \"bg-yellow-50 border-yellow-200 hover:bg-yellow-100\", \" \").concat(selectedGroup === group.id ? \"ring-4 ring-blue-300\" : \"\"),\n                            onClick: ()=>setSelectedGroup(selectedGroup === group.id ? null : group.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4 \".concat(index === 0 ? \"text-blue-700\" : index === 1 ? \"text-green-700\" : \"text-yellow-700\"),\n                                    children: group.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mb-3\",\n                                    children: group.production\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"عدد الحيوانات:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            value: group.count,\n                                            onChange: (e)=>updateAnimalCount(\"group\".concat(group.id), parseInt(e.target.value) || 0),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• علف مركز: \",\n                                                group.concentrate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• علف خشن: \",\n                                                group.roughage\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• بروتين: \",\n                                                group.protein\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• طاقة: \",\n                                                group.energy\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                group.count > 0 && showCalculation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-white rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-sm mb-2\",\n                                            children: \"الكميات المطلوبة يومياً:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"علف مركز: \",\n                                                        calculateTotalFeed(group).concentrate,\n                                                        \" كجم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"علف خشن: \",\n                                                        calculateTotalFeed(group).roughage,\n                                                        \" كجم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-bold\",\n                                                    children: [\n                                                        \"المجموع: \",\n                                                        calculateTotalFeed(group).total,\n                                                        \" كجم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center gap-4 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGroupClassification,\n                            className: \"button-3d px-8 py-3 rounded-lg bg-blue-200 hover:bg-blue-300 font-bold transition-colors duration-300\",\n                            children: \"تصنيف القطيع\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleQuantityCalculation,\n                            className: \"button-3d px-8 py-3 rounded-lg bg-green-200 hover:bg-green-300 font-bold transition-colors duration-300\",\n                            children: \"حساب الكميات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                showCalculation && (animalCounts.group1 > 0 || animalCounts.group2 > 0 || animalCounts.group3 > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 p-6 bg-gray-50 rounded-lg border-2 border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-center text-gray-700 mb-4\",\n                            children: \"ملخص الكميات المطلوبة يومياً\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-4 rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-blue-600 mb-2\",\n                                            children: \"إجمالي العلف المركز:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-blue-700\",\n                                            children: [\n                                                groups.reduce((total, group)=>total + parseFloat(calculateTotalFeed(group).concentrate), 0).toFixed(1),\n                                                \" كجم\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-4 rounded border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold text-green-600 mb-2\",\n                                            children: \"إجمالي العلف الخشن:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-green-700\",\n                                            children: [\n                                                groups.reduce((total, group)=>total + parseFloat(calculateTotalFeed(group).roughage), 0).toFixed(1),\n                                                \" كجم\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-bold text-gray-600 mb-2\",\n                                        children: \"إجمالي العلف المطلوب:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-gray-700\",\n                                        children: [\n                                            groups.reduce((total, group)=>total + parseFloat(calculateTotalFeed(group).total), 0).toFixed(1),\n                                            \" كجم/يوم\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\alaf\\\\src\\\\app\\\\ration-division\\\\page.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(RationDivision, \"bdmBQLUagHn6SkdYau0yqyY+AhI=\");\n_c = RationDivision;\nvar _c;\n$RefreshReg$(_c, \"RationDivision\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ration-division/page.tsx\n"));

/***/ })

});