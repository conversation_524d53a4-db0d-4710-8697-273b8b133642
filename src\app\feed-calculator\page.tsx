'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'

interface FeedItem {
  name: string
  protein: number
  energy: number
  fiber: number
  price: number
}

interface RationRow {
  feedName: string
  quantity: number
  protein: number
  energy: number
  cost: number
}

export default function FeedCalculator() {
  const router = useRouter()
  const printRef = useRef<HTMLDivElement>(null)
  const [selectedFeed, setSelectedFeed] = useState('')

  // Animal requirements
  const [animalWeight, setAnimalWeight] = useState(500)
  const [milkProduction, setMilkProduction] = useState(25)
  const [productionStage, setProductionStage] = useState('ذروة الإنتاج')
  // Feed data - moved before useState
  const feedData: FeedItem[] = [
    { name: 'الذرة الصفراء', protein: 8.5, energy: 3200, fiber: 2.1, price: 320 },
    { name: 'الشعير', protein: 11.5, energy: 2800, fiber: 5.2, price: 270 },
    { name: 'كسبة فول الصويا', protein: 44.0, energy: 2200, fiber: 7.0, price: 600 },
    { name: 'البرسيم الحجازي', protein: 18.0, energy: 2400, fiber: 25.0, price: 230 },
    { name: 'التبن', protein: 4.0, energy: 1800, fiber: 40.0, price: 110 },
    { name: 'النخالة', protein: 15.5, energy: 2000, fiber: 12.0, price: 200 },
    { name: 'الدريس', protein: 12.0, energy: 2100, fiber: 28.0, price: 140 },
    { name: 'السيلاج', protein: 8.0, energy: 2300, fiber: 22.0, price: 130 }
  ]

  const [showPriceEditor, setShowPriceEditor] = useState(false)
  const [editableFeedData, setEditableFeedData] = useState(feedData)

  // Ration composition
  const [ration, setRation] = useState<RationRow[]>(
    Array(8).fill(null).map(() => ({
      feedName: '',
      quantity: 0,
      protein: 0,
      energy: 0,
      cost: 0
    }))
  )

  // Calculate requirements based on animal data
  const calculateRequirements = () => {
    // التأكد من أن القيم موجبة
    const weight = Math.max(animalWeight || 0, 0)
    const milk = Math.max(milkProduction || 0, 0)

    const baseProtein = (weight * 0.004) + (milk * 0.08) // كجم بروتين
    const baseEnergy = (weight * 0.05) + (milk * 1.2) // ميجا كالوري
    const baseDryMatter = weight * 0.03 + milk * 0.4 // كجم مادة جافة

    const stageMultiplier = {
      'بداية الإدرار': 1.2,
      'ذروة الإنتاج': 1.3,
      'منتصف الإدرار': 1.1,
      'نهاية الإدرار': 0.9
    }[productionStage] || 1.1

    return {
      protein: Math.max(baseProtein * stageMultiplier, 0),
      energy: Math.max(baseEnergy * stageMultiplier, 0),
      dryMatter: Math.max(baseDryMatter * stageMultiplier, 0)
    }
  }

  const requirements = calculateRequirements()

  // Load saved prices on component mount
  useEffect(() => {
    const savedPrices = localStorage.getItem('customFeedPrices')
    if (savedPrices) {
      try {
        setEditableFeedData(JSON.parse(savedPrices))
      } catch (error) {
        console.error('Error loading saved prices:', error)
        setEditableFeedData(feedData)
      }
    }
  }, [])

  // Update ration calculations
  const updateRationRow = (index: number, field: string, value: string | number) => {
    const newRation = [...ration]

    if (field === 'feedName') {
      const selectedFeedData = editableFeedData.find(f => f.name === value)
      newRation[index] = {
        ...newRation[index],
        feedName: value as string,
        protein: selectedFeedData ? (newRation[index].quantity * selectedFeedData.protein / 100) : 0,
        energy: selectedFeedData ? (newRation[index].quantity * selectedFeedData.energy / 1000) : 0,
        cost: selectedFeedData ? (newRation[index].quantity * selectedFeedData.price / 1000) : 0
      }
    } else if (field === 'quantity') {
      const selectedFeedData = editableFeedData.find(f => f.name === newRation[index].feedName)
      const qty = Number(value) || 0
      newRation[index] = {
        ...newRation[index],
        quantity: qty,
        protein: selectedFeedData ? (qty * selectedFeedData.protein / 100) : 0,
        energy: selectedFeedData ? (qty * selectedFeedData.energy / 1000) : 0,
        cost: selectedFeedData ? (qty * selectedFeedData.price / 1000) : 0
      }
    }

    setRation(newRation)
  }

  // Add new row
  const addNewRow = () => {
    setRation([...ration, {
      feedName: '',
      quantity: 0,
      protein: 0,
      energy: 0,
      cost: 0
    }])
  }

  // Delete row
  const deleteRow = (index: number) => {
    if (ration.length > 1) {
      const newRation = ration.filter((_, i) => i !== index)
      setRation(newRation)
    } else {
      // Reset the row if it's the last one
      const newRation = [...ration]
      newRation[index] = {
        feedName: '',
        quantity: 0,
        protein: 0,
        energy: 0,
        cost: 0
      }
      setRation(newRation)
    }
  }

  // Clear all rows
  const clearAllRows = () => {
    setRation(Array(5).fill(null).map(() => ({
      feedName: '',
      quantity: 0,
      protein: 0,
      energy: 0,
      cost: 0
    })))
  }

  // Update feed price
  const updateFeedPrice = (feedName: string, newPrice: number) => {
    const updatedFeedData = editableFeedData.map(feed =>
      feed.name === feedName ? { ...feed, price: newPrice } : feed
    )
    setEditableFeedData(updatedFeedData)

    // Update existing ration calculations
    const updatedRation = ration.map(row => {
      if (row.feedName === feedName) {
        const updatedFeed = updatedFeedData.find(f => f.name === feedName)
        return {
          ...row,
          cost: updatedFeed ? (row.quantity * updatedFeed.price / 1000) : row.cost
        }
      }
      return row
    })
    setRation(updatedRation)
  }

  // Save prices to localStorage
  const savePrices = () => {
    try {
      localStorage.setItem('customFeedPrices', JSON.stringify(editableFeedData))
      alert('تم حفظ الأسعار بنجاح!')
    } catch (error) {
      console.error('Error saving prices:', error)
      alert('حدث خطأ أثناء حفظ الأسعار!')
    }
  }

  // Load prices from localStorage
  const loadPrices = () => {
    try {
      const savedPrices = localStorage.getItem('customFeedPrices')
      if (savedPrices) {
        setEditableFeedData(JSON.parse(savedPrices))
        alert('تم تحميل الأسعار المحفوظة!')
      } else {
        alert('لا توجد أسعار محفوظة!')
      }
    } catch (error) {
      console.error('Error loading prices:', error)
      alert('حدث خطأ أثناء تحميل الأسعار!')
    }
  }

  // Reset prices to default
  const resetPrices = () => {
    setEditableFeedData([...feedData])
    localStorage.removeItem('customFeedPrices')
    alert('تم إعادة تعيين الأسعار للقيم الافتراضية!')
  }

  // Calculate totals
  const totals = ration.reduce((acc, row) => ({
    quantity: acc.quantity + (Number(row.quantity) || 0),
    protein: acc.protein + (Number(row.protein) || 0),
    energy: acc.energy + (Number(row.energy) || 0),
    cost: acc.cost + (Number(row.cost) || 0)
  }), { quantity: 0, protein: 0, energy: 0, cost: 0 })

  // Print function
  const handlePrint = () => {
    const printContent = printRef.current
    if (printContent) {
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>تقرير تكوين العليقة</title>
              <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #000; padding: 8px; text-align: center; }
                th { background-color: #f0f0f0; }
                .header { text-align: center; margin-bottom: 30px; }
                .summary { margin: 20px 0; }
              </style>
            </head>
            <body>
              ${printContent.innerHTML}
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.print()
      }
    }
  }

  // Save ration
  const saveRation = () => {
    const rationData = {
      animalWeight,
      milkProduction,
      productionStage,
      ration: ration.filter(row => row.feedName && row.quantity > 0),
      totals,
      requirements,
      date: new Date().toLocaleDateString('ar-SA')
    }

    localStorage.setItem('savedRation', JSON.stringify(rationData))
    alert('تم حفظ العليقة بنجاح!')
  }

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.push('/')}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              ← العودة للصفحة الرئيسية
            </button>
            <h1 className="text-2xl font-bold text-center text-blue-800">
              دراسة الجدوى الاقتصادية - برنامج تكوين العلائق
            </h1>
            <div className="w-20"></div>
          </div>
        </div>

        {/* Print Content */}
        <div ref={printRef}>
          <div className="header" style={{ display: 'none' }}>
            <h1>تقرير تكوين العليقة</h1>
            <p>التاريخ: {new Date().toLocaleDateString('ar-SA')}</p>
            <p>وزن الحيوان: {animalWeight} كجم | إنتاج الحليب: {milkProduction} لتر</p>
          </div>

        <div className="grid grid-cols-12 gap-4">
          {/* Left Panel - Feed List */}
          <div className="col-span-3">
            <div className="bg-red-600 text-white p-3 rounded-t-lg">
              <h3 className="text-center font-bold">مواد العلف المتاحة</h3>
              <p className="text-center text-sm">ترتيب حسب التكلفة المنخفضة</p>
            </div>
            <div className="bg-white border border-gray-300 rounded-b-lg">
              {editableFeedData.map((feed, index) => (
                <div 
                  key={index}
                  className={`p-2 border-b border-gray-200 cursor-pointer hover:bg-blue-50 ${
                    selectedFeed === feed.name ? 'bg-blue-100' : ''
                  }`}
                  onClick={() => setSelectedFeed(feed.name)}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{feed.name}</span>
                    <span className="text-xs text-gray-600">{feed.price} دينار</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    بروتين: {feed.protein}% | طاقة: {feed.energy}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Center Panel - Calculation Table */}
          <div className="col-span-6">
            <div className="bg-green-600 text-white p-3 rounded-t-lg">
              <h3 className="text-center font-bold">جدول حساب تكوين العليقة</h3>
            </div>
            <div className="bg-white border border-gray-300 rounded-b-lg overflow-hidden">
              <table className="w-full text-sm">
                <thead className="bg-gray-200">
                  <tr>
                    <th className="border border-gray-300 p-2">المادة العلفية</th>
                    <th className="border border-gray-300 p-2">الكمية (كجم)</th>
                    <th className="border border-gray-300 p-2">البروتين</th>
                    <th className="border border-gray-300 p-2">الطاقة</th>
                    <th className="border border-gray-300 p-2">التكلفة</th>
                    <th className="border border-gray-300 p-2">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {ration.map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="border border-gray-300 p-2">
                        <select
                          className="w-full text-xs"
                          value={row.feedName}
                          onChange={(e) => updateRationRow(index, 'feedName', e.target.value)}
                        >
                          <option value="">اختر المادة</option>
                          {editableFeedData.map((feed, feedIndex) => (
                            <option key={feedIndex} value={feed.name}>{feed.name}</option>
                          ))}
                        </select>
                      </td>
                      <td className="border border-gray-300 p-2">
                        <input
                          type="number"
                          className="w-full text-xs p-1"
                          placeholder="0"
                          value={row.quantity || ''}
                          onChange={(e) => updateRationRow(index, 'quantity', e.target.value)}
                        />
                      </td>
                      <td className="border border-gray-300 p-2 text-center">
                        {row.protein > 0 ? row.protein.toFixed(2) : '--'}
                      </td>
                      <td className="border border-gray-300 p-2 text-center">
                        {row.energy > 0 ? row.energy.toFixed(1) : '--'}
                      </td>
                      <td className="border border-gray-300 p-2 text-center">
                        {row.cost > 0 ? row.cost.toFixed(2) : '--'}
                      </td>
                      <td className="border border-gray-300 p-2 text-center">
                        <div className="flex gap-1 justify-center">
                          <button
                            onClick={() => deleteRow(index)}
                            className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
                            title="حذف الصف"
                          >
                            🗑️
                          </button>
                          <button
                            onClick={() => {
                              // Duplicate row
                              const newRation = [...ration]
                              newRation.splice(index + 1, 0, { ...row })
                              setRation(newRation)
                            }}
                            className="px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
                            title="نسخ الصف"
                          >
                            📋
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                  <tr className="bg-yellow-100 font-bold">
                    <td className="border border-gray-300 p-2">المجموع</td>
                    <td className="border border-gray-300 p-2 text-center">
                      {totals.quantity.toFixed(1)}
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      {totals.protein.toFixed(2)}
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      {totals.energy.toFixed(1)}
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      {totals.cost.toFixed(2)}
                    </td>
                    <td className="border border-gray-300 p-2 text-center">
                      --
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Bottom Buttons */}
            <div className="bg-green-700 p-3 rounded-b-lg">
              <div className="flex justify-center gap-2 flex-wrap">
                <button
                  onClick={addNewRow}
                  className="px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                  title="إضافة صف جديد"
                >
                  ➕ إضافة صف
                </button>
                <button
                  onClick={clearAllRows}
                  className="px-3 py-2 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                  title="مسح جميع الصفوف"
                >
                  🗑️ مسح الكل
                </button>
                <button
                  onClick={() => {
                    // Auto calculate - already happening in real time
                    alert('تم حساب التكوين تلقائياً!')
                  }}
                  className="px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                >
                  📊 حساب التكوين
                </button>
                <button
                  onClick={saveRation}
                  className="px-3 py-2 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
                >
                  💾 حفظ العليقة
                </button>
                <button
                  onClick={handlePrint}
                  className="px-3 py-2 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
                >
                  🖨️ طباعة النتائج
                </button>
                <button
                  onClick={() => {
                    const savedRation = localStorage.getItem('savedRation')
                    if (savedRation) {
                      const rationData = JSON.parse(savedRation)
                      setRation(rationData.ration || rationData)
                      if (rationData.animalWeight) setAnimalWeight(rationData.animalWeight)
                      if (rationData.milkProduction) setMilkProduction(rationData.milkProduction)
                      if (rationData.productionStage) setProductionStage(rationData.productionStage)
                      alert('تم تحميل العليقة المحفوظة!')
                    } else {
                      alert('لا توجد عليقة محفوظة!')
                    }
                  }}
                  className="px-3 py-2 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
                >
                  📂 تحميل العليقة
                </button>
                <button
                  onClick={() => setShowPriceEditor(true)}
                  className="px-3 py-2 bg-indigo-500 text-white rounded text-sm hover:bg-indigo-600"
                >
                  💰 تعديل الأسعار
                </button>
              </div>
            </div>
          </div>

          {/* Right Panel - Requirements & Results */}
          <div className="col-span-3">
            {/* Animal Requirements */}
            <div className="mb-4">
              <div className="bg-blue-600 text-white p-3 rounded-t-lg">
                <h3 className="text-center font-bold text-sm">احتياجات الحيوان</h3>
              </div>
              <div className="bg-white border border-gray-300 rounded-b-lg p-3">
                <div className="space-y-2 text-sm">
                  <div>
                    <label className="block font-bold mb-1">وزن الحيوان (كجم)</label>
                    <input
                      type="number"
                      className="w-full p-2 border rounded"
                      value={animalWeight}
                      onChange={(e) => setAnimalWeight(Number(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <label className="block font-bold mb-1">إنتاج الحليب (لتر)</label>
                    <input
                      type="number"
                      className="w-full p-2 border rounded"
                      value={milkProduction}
                      onChange={(e) => setMilkProduction(Number(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <label className="block font-bold mb-1">مرحلة الإنتاج</label>
                    <select
                      className="w-full p-2 border rounded text-sm"
                      value={productionStage}
                      onChange={(e) => setProductionStage(e.target.value)}
                    >
                      <option value="بداية الإدرار">بداية الإدرار</option>
                      <option value="ذروة الإنتاج">ذروة الإنتاج</option>
                      <option value="منتصف الإدرار">منتصف الإدرار</option>
                      <option value="نهاية الإدرار">نهاية الإدرار</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Nutritional Analysis */}
            <div className="mb-4">
              <div className="bg-orange-600 text-white p-3 rounded-t-lg">
                <h3 className="text-center font-bold text-sm">التحليل الغذائي</h3>
              </div>
              <div className="bg-white border border-gray-300 rounded-b-lg p-3">
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span>البروتين المطلوب:</span>
                    <span className="font-bold">{requirements.protein.toFixed(2)} كجم</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الطاقة المطلوبة:</span>
                    <span className="font-bold">{requirements.energy.toFixed(1)} ميجا كالوري</span>
                  </div>
                  <div className="flex justify-between">
                    <span>المادة الجافة:</span>
                    <span className="font-bold">{requirements.dryMatter.toFixed(1)} كجم</span>
                  </div>
                  <hr />
                  <div className="flex justify-between text-green-600">
                    <span>البروتين المحسوب:</span>
                    <span className="font-bold">{totals.protein.toFixed(2)} كجم</span>
                  </div>
                  <div className="flex justify-between text-green-600">
                    <span>الطاقة المحسوبة:</span>
                    <span className="font-bold">{totals.energy.toFixed(1)} ميجا كالوري</span>
                  </div>
                  <hr />
                  <div className="flex justify-between">
                    <span>نسبة تحقق البروتين:</span>
                    <span className={`font-bold ${
                      totals.protein >= requirements.protein * 0.95 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {requirements.protein > 0 ? Math.max(((totals.protein / requirements.protein) * 100), 0).toFixed(1) : '0.0'}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>نسبة تحقق الطاقة:</span>
                    <span className={`font-bold ${
                      totals.energy >= requirements.energy * 0.95 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {requirements.energy > 0 ? Math.max(((totals.energy / requirements.energy) * 100), 0).toFixed(1) : '0.0'}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Cost Analysis */}
            <div>
              <div className="bg-purple-600 text-white p-3 rounded-t-lg">
                <h3 className="text-center font-bold text-sm">تحليل التكلفة</h3>
              </div>
              <div className="bg-white border border-gray-300 rounded-b-lg p-3">
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span>تكلفة العليقة اليومية:</span>
                    <span className="font-bold">{totals.cost.toFixed(2)} دينار</span>
                  </div>
                  <div className="flex justify-between">
                    <span>تكلفة شهرية:</span>
                    <span className="font-bold">{(totals.cost * 30).toFixed(2)} دينار</span>
                  </div>
                  <div className="flex justify-between">
                    <span>تكلفة سنوية:</span>
                    <span className="font-bold">{(totals.cost * 365).toFixed(2)} دينار</span>
                  </div>
                  <hr />
                  <div className="flex justify-between text-blue-600">
                    <span>تكلفة لتر الحليب:</span>
                    <span className="font-bold">
                      {milkProduction > 0 ? Math.max((totals.cost / milkProduction), 0).toFixed(2) : '0.00'} دينار
                    </span>
                  </div>
                  <div className="flex justify-between text-purple-600">
                    <span>الربحية (إذا كان سعر اللتر 0.6 دينار):</span>
                    <span className="font-bold">
                      {milkProduction > 0 ? Math.max(((0.6 - (totals.cost / milkProduction)) * milkProduction), 0).toFixed(2) : '0.00'} دينار/يوم
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>

        {/* Price Editor Modal */}
        {showPriceEditor && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-blue-700">تعديل أسعار المواد العلفية</h2>
                <button
                  onClick={() => setShowPriceEditor(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                {editableFeedData.map((feed, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <h3 className="font-bold text-gray-800">{feed.name}</h3>
                      <p className="text-sm text-gray-600">
                        بروتين: {feed.protein}% | طاقة: {feed.energy} كالوري | ألياف: {feed.fiber}%
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        value={feed.price}
                        onChange={(e) => updateFeedPrice(feed.name, Number(e.target.value) || 0)}
                        className="w-24 p-2 border rounded text-center"
                        min="0"
                        step="10"
                      />
                      <span className="text-sm text-gray-600">دينار/طن</span>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center gap-3 mt-6">
                <button
                  onClick={savePrices}
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                >
                  💾 حفظ الأسعار
                </button>
                <button
                  onClick={loadPrices}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  📂 تحميل الأسعار
                </button>
                <button
                  onClick={resetPrices}
                  className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                >
                  🔄 إعادة تعيين
                </button>
                <button
                  onClick={() => setShowPriceEditor(false)}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
